import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

class LoginTestView extends GetView<AuthController> {
  const LoginTestView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار GetX Data Binding'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختبار ربط البيانات مع GetX',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Username field
            const Text('اسم المستخدم:', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            Obx(() => TextField(
              controller: controller.usernameController,
              decoration: InputDecoration(
                hintText: 'أدخل اسم المستخدم',
                border: const OutlineInputBorder(),
                suffixText: 'الطول: ${controller.username.value.length}',
              ),
            )),
            const SizedBox(height: 16),
            
            // Password field
            const Text('كلمة المرور:', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            Obx(() => TextField(
              controller: controller.passwordController,
              obscureText: controller.obscurePassword.value,
              decoration: InputDecoration(
                hintText: 'أدخل كلمة المرور',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    controller.obscurePassword.value
                        ? Icons.visibility
                        : Icons.visibility_off,
                  ),
                  onPressed: controller.togglePasswordVisibility,
                ),
              ),
            )),
            const SizedBox(height: 20),
            
            // Display current values
            const Text(
              'القيم الحالية:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Obx(() => Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('اسم المستخدم: "${controller.username.value}"'),
                    const SizedBox(height: 8),
                    Text('كلمة المرور: "${controller.password.value}"'),
                    const SizedBox(height: 8),
                    Text('إخفاء كلمة المرور: ${controller.obscurePassword.value}'),
                    const SizedBox(height: 8),
                    Text('رسالة الخطأ: "${controller.errorMessage.value}"'),
                    const SizedBox(height: 8),
                    Text('جاري التحميل: ${controller.isLoading.value}'),
                  ],
                ),
              ),
            )),
            const SizedBox(height: 20),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.clearForm(),
                    child: const Text('مسح الحقول'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      controller.usernameController.text = 'admin';
                      controller.passwordController.text = 'admin123';
                    },
                    child: const Text('ملء تجريبي'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: controller.togglePasswordVisibility,
                child: const Text('تبديل إظهار/إخفاء كلمة المرور'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
