import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/product_controller.dart';
import '../controllers/auth_controller.dart';
import '../routes/app_routes.dart';

class DebugView extends StatelessWidget {
  const DebugView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص التطبيق'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة Controllers:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Check ProductController
            _buildControllerStatus('ProductController', () {
              try {
                final controller = Get.find<ProductController>();
                return 'موجود - المنتجات: ${controller.products.length}';
              } catch (e) {
                return 'خطأ: $e';
              }
            }),
            
            // Check AuthController
            _buildControllerStatus('AuthController', () {
              try {
                final controller = Get.find<AuthController>();
                return 'موجود - المستخدم: ${controller.currentUser.value?.fullName ?? 'غير مسجل'}';
              } catch (e) {
                return 'خطأ: $e';
              }
            }),
            
            const SizedBox(height: 32),
            const Text(
              'اختبار التنقل:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Navigation tests
            ElevatedButton(
              onPressed: () {
                try {
                  Get.toNamed(AppRoutes.products);
                } catch (e) {
                  _showError(context, 'خطأ في التنقل للمنتجات: $e');
                }
              },
              child: const Text('اختبار التنقل للمنتجات'),
            ),
            
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                try {
                  Get.to(() => const TestProductsView());
                } catch (e) {
                  _showError(context, 'خطأ في التنقل المباشر: $e');
                }
              },
              child: const Text('تنقل مباشر للمنتجات'),
            ),
            
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                try {
                  final controller = Get.find<ProductController>();
                  controller.initializeDemoProducts();
                  _showSuccess(context, 'تم تهيئة المنتجات التجريبية');
                } catch (e) {
                  _showError(context, 'خطأ في تهيئة المنتجات: $e');
                }
              },
              child: const Text('تهيئة المنتجات التجريبية'),
            ),

            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                try {
                  Get.toNamed(AppRoutes.loginTest);
                } catch (e) {
                  _showError(context, 'خطأ في التنقل لاختبار GetX: $e');
                }
              },
              child: const Text('اختبار GetX Data Binding'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControllerStatus(String name, String Function() statusGetter) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(statusGetter()),
            ),
          ],
        ),
      ),
    );
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}

// Test Products View
class TestProductsView extends GetView<ProductController> {
  const TestProductsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المنتجات'),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'عدد المنتجات: ${controller.products.length}',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            
            if (controller.products.isEmpty)
              const Expanded(
                child: Center(
                  child: Text('لا توجد منتجات'),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: controller.products.length,
                  itemBuilder: (context, index) {
                    final product = controller.products[index];
                    return ListTile(
                      title: Text(product.name),
                      subtitle: Text('${product.price} ر.س'),
                      trailing: Text('المخزون: ${product.quantity}'),
                    );
                  },
                ),
              ),
          ],
        );
      }),
    );
  }
}
