{"logs": [{"outputFile": "com.example.pos.pos_app-mergeDebugResources-39:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,346,483,652,736", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "172,260,341,478,647,731,812"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3534,3606,3694,3775,4095,4264,4348", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "3601,3689,3770,3907,4259,4343,4424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a04377ab98aaf9189ccddcb951bae89\\transformed\\appcompat-1.1.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,80,90,91,94,93,100,92,94,93,90,90,81,105,105,98,109,107,100,169,96,81", "endOffsets": "208,308,418,507,613,730,812,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1912,2018,2124,2223,2333,2441,2542,2712,2809,2891"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1917,2023,2129,2228,2338,2446,2547,2717,3912", "endColumns": "107,99,109,88,105,116,81,80,90,91,94,93,100,92,94,93,90,90,81,105,105,98,109,107,100,169,96,81", "endOffsets": "208,308,418,507,613,730,812,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1912,2018,2124,2223,2333,2441,2542,2712,2809,3989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2814,2914,3019,3117,3216,3321,3423,3994", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "2909,3014,3112,3211,3316,3418,3529,4090"}}]}]}