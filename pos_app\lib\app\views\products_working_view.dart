import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProductsView extends StatelessWidget {
  const ProductsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنتجات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Get.snackbar('تحديث', 'تم تحديث المنتجات');
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showAddProductDialog(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'البحث في المنتجات',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                // Search functionality
              },
            ),
          ),

          // Category filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildCategoryChip('الكل', true),
                _buildCategoryChip('طعام', false),
                _buildCategoryChip('مشروبات', false),
                _buildCategoryChip('إلكترونيات', false),
                _buildCategoryChip('ملابس', false),
              ],
            ),
          ),

          // Products list
          Expanded(
            child: ListView.builder(
              itemCount: _getDemoProducts().length,
              itemBuilder: (context, index) {
                final product = _getDemoProducts()[index];
                return _buildProductCard(context, product);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddProductDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoryChip(String label, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          Get.snackbar('فئة', 'تم اختيار فئة: $label');
        },
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, Map<String, dynamic> product) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.inventory, color: Colors.grey),
        ),
        title: Text(
          product['name'],
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(product['description']),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  'السعر: ${product['price']} ر.س',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'المخزون: ${product['quantity']}',
                  style: TextStyle(
                    color: product['quantity'] < 10 ? Colors.red : Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (product['quantity'] < 10)
              const Text(
                'تحذير: المخزون منخفض',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleProductAction(context, value, product),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleProductAction(BuildContext context, String action, Map<String, dynamic> product) {
    switch (action) {
      case 'view':
        _showProductDetails(context, product);
        break;
      case 'edit':
        _showEditProductDialog(context, product);
        break;
      case 'delete':
        _showDeleteConfirmation(context, product);
        break;
    }
  }

  void _showAddProductDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة منتج جديد'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(labelText: 'اسم المنتج'),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(labelText: 'السعر'),
              keyboardType: TextInputType.number,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(labelText: 'الكمية'),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.snackbar('نجح', 'تم إضافة المنتج بنجاح');
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditProductDialog(BuildContext context, Map<String, dynamic> product) {
    Get.snackbar('تعديل', 'تعديل ${product['name']} - قريباً');
  }

  void _showProductDetails(BuildContext context, Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(product['name']),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الوصف: ${product['description']}'),
            Text('الباركود: ${product['barcode']}'),
            Text('الفئة: ${product['category']}'),
            Text('السعر: ${product['price']} ر.س'),
            Text('التكلفة: ${product['cost']} ر.س'),
            Text('الكمية: ${product['quantity']}'),
            if (product['quantity'] < 10)
              const Text(
                'تحذير: المخزون منخفض',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من رغبتك في حذف "${product['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Get.snackbar('تم الحذف', 'تم حذف ${product['name']} بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoProducts() {
    return [
      {
        'name': 'كوكا كولا 330مل',
        'description': 'مشروب غازي كوكا كولا علبة 330 مل',
        'barcode': '1234567890123',
        'category': 'مشروبات',
        'price': 2.50,
        'cost': 1.75,
        'quantity': 100,
      },
      {
        'name': 'خبز أبيض',
        'description': 'خبز أبيض طازج',
        'barcode': '1234567890124',
        'category': 'طعام',
        'price': 1.00,
        'cost': 0.60,
        'quantity': 50,
      },
      {
        'name': 'حليب كامل الدسم 1 لتر',
        'description': 'حليب طازج كامل الدسم 1 لتر',
        'barcode': '1234567890125',
        'category': 'مشروبات',
        'price': 4.50,
        'cost': 3.20,
        'quantity': 5, // Low stock
      },
      {
        'name': 'أرز بسمتي 1 كيلو',
        'description': 'أرز بسمتي فاخر 1 كيلوجرام',
        'barcode': '1234567890126',
        'category': 'طعام',
        'price': 8.00,
        'cost': 5.50,
        'quantity': 25,
      },
      {
        'name': 'شامبو للشعر',
        'description': 'شامبو طبيعي للشعر 400 مل',
        'barcode': '1234567890127',
        'category': 'صحة',
        'price': 15.00,
        'cost': 10.00,
        'quantity': 15,
      },
    ];
  }
}
