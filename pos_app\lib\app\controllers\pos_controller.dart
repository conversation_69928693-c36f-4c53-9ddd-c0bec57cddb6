import 'package:get/get.dart';
import '../data/models/product_model.dart';
import 'product_controller.dart';
import 'invoice_controller.dart';

class POSController extends GetxController {
  final ProductController _productController = Get.find<ProductController>();
  final InvoiceController _invoiceController = Get.find<InvoiceController>();

  // Form fields as reactive variables
  final RxString barcodeText = ''.obs;
  final RxString customerName = ''.obs;
  final RxString customerPhone = ''.obs;
  final RxString paidAmount = ''.obs;

  // UI state
  final RxBool isProcessingSale = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize demo products if needed
    _productController.initializeDemoProducts();
    
    // Listen to customer info changes
    ever(customerName, (name) => _updateCustomerInfo());
    ever(customerPhone, (phone) => _updateCustomerInfo());
  }

  // Getters for easy access
  ProductController get productController => _productController;
  InvoiceController get invoiceController => _invoiceController;

  void _updateCustomerInfo() {
    _invoiceController.setCustomerInfo(customerName.value, customerPhone.value);
  }

  void onBarcodeSubmitted(String barcode) {
    if (barcode.isNotEmpty) {
      final success = _invoiceController.addProductByBarcode(barcode);
      if (success) {
        barcodeText.value = '';
        successMessage.value = 'تم إضافة المنتج بنجاح';
        _clearMessages();
      } else {
        errorMessage.value = _invoiceController.errorMessage.value;
        _clearMessages();
      }
    }
  }

  void onBarcodeChanged(String value) {
    barcodeText.value = value;
    _productController.searchProducts(value);
  }

  void addProductToCart(ProductModel product, int quantity) {
    _invoiceController.addItemToInvoice(product, quantity);
    successMessage.value = 'تم إضافة ${product.name} إلى السلة';
    _clearMessages();
  }

  void clearInvoice() {
    _invoiceController.clearCurrentInvoice();
    customerName.value = '';
    customerPhone.value = '';
    paidAmount.value = '';
    successMessage.value = 'تم مسح الفاتورة';
    _clearMessages();
  }

  Future<String?> processSale() async {
    try {
      isProcessingSale.value = true;
      errorMessage.value = '';

      final paidAmountValue = double.tryParse(paidAmount.value) ?? 0;
      
      if (paidAmountValue < _invoiceController.totalAmount.value) {
        errorMessage.value = 'المبلغ المدفوع أقل من الإجمالي';
        return null;
      }

      final invoiceNumber = await _invoiceController.createInvoice(
        paidAmount: paidAmountValue,
      );

      if (invoiceNumber != null) {
        // Clear form
        paidAmount.value = '';
        customerName.value = '';
        customerPhone.value = '';
        
        successMessage.value = 'تم إتمام البيع بنجاح';
        _clearMessages();
        
        return invoiceNumber;
      } else {
        errorMessage.value = _invoiceController.errorMessage.value;
        return null;
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء معالجة البيع: $e';
      return null;
    } finally {
      isProcessingSale.value = false;
    }
  }

  void _clearMessages() {
    Future.delayed(const Duration(seconds: 3), () {
      errorMessage.value = '';
      successMessage.value = '';
    });
  }

  void clearSearch() {
    barcodeText.value = '';
    _productController.clearSearch();
  }

  void selectCategory(String category) {
    _productController.filterByCategory(category);
  }

  void setPaymentMethod(String method) {
    _invoiceController.setPaymentMethod(method);
  }

  void setDiscount(double discount) {
    _invoiceController.setDiscount(discount);
  }

  // Validation helpers
  bool get canProcessSale => 
    _invoiceController.currentInvoiceItems.isNotEmpty && 
    !isProcessingSale.value;

  bool get hasItems => _invoiceController.currentInvoiceItems.isNotEmpty;

  double get changeAmount {
    final paid = double.tryParse(paidAmount.value) ?? 0;
    final total = _invoiceController.totalAmount.value;
    return paid > total ? paid - total : 0;
  }

  @override
  void onClose() {
    // Clean up if needed
    super.onClose();
  }
}
