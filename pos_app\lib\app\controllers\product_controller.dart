import 'package:get/get.dart';
import '../data/models/product_model.dart';
import '../data/models/user_model.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';
import 'auth_controller.dart';

class ProductController extends GetxController {
  final DatabaseService _databaseService = Get.find<DatabaseService>();
  final ApiService _apiService = Get.find<ApiService>();
  final AuthController _authController = Get.find<AuthController>();

  final RxList<ProductModel> products = <ProductModel>[].obs;
  final RxList<ProductModel> filteredProducts = <ProductModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;
  final RxString selectedCategory = 'all'.obs;

  @override
  void onInit() {
    super.onInit();
    loadProducts();
    
    // Listen to search query changes
    debounce(searchQuery, (_) => _filterProducts(), time: const Duration(milliseconds: 500));
    ever(selectedCategory, (_) => _filterProducts());
  }

  Future<void> loadProducts() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final localProducts = await _databaseService.getAllProducts();
      products.value = localProducts;
      _filterProducts();

      // Sync with ERPNext if configured
      if (_apiService.isConfigured) {
        await syncWithERP();
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل المنتجات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> syncWithERP() async {
    try {
      if (!_apiService.isConfigured) return;

      final erpItems = await _apiService.getItems();
      
      for (final item in erpItems) {
        final product = ProductModel(
          id: item['name'] ?? '',
          name: item['item_name'] ?? '',
          description: item['description'] ?? '',
          barcode: item['item_code'] ?? '',
          category: item['item_group'] ?? ProductCategories.other,
          price: (item['standard_rate'] ?? 0).toDouble(),
          cost: (item['standard_rate'] ?? 0).toDouble() * 0.7, // Assume 30% margin
          quantity: 100, // Default quantity
          minQuantity: 10,
          unit: ProductUnits.piece,
          isActive: true,
          createdAt: DateTime.now(),
          erpData: item,
        );

        await _databaseService.insertProduct(product);
      }

      await loadProducts();
    } catch (e) {
      print('Error syncing with ERP: $e');
    }
  }

  void _filterProducts() {
    var filtered = products.where((product) {
      final matchesSearch = searchQuery.value.isEmpty ||
          product.name.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
          product.barcode.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
          product.description.toLowerCase().contains(searchQuery.value.toLowerCase());

      final matchesCategory = selectedCategory.value == 'all' ||
          product.category == selectedCategory.value;

      return matchesSearch && matchesCategory && product.isActive;
    }).toList();

    filteredProducts.value = filtered;
  }

  void searchProducts(String query) {
    searchQuery.value = query;
  }

  void filterByCategory(String category) {
    selectedCategory.value = category;
  }

  Future<bool> addProduct({
    required String name,
    required String description,
    required String barcode,
    required String category,
    required double price,
    required double cost,
    required int quantity,
    required int minQuantity,
    required String unit,
    String? imageUrl,
  }) async {
    try {
      if (!_authController.hasPermission(UserPermissions.createProduct)) {
        errorMessage.value = 'ليس لديك صلاحية لإضافة منتجات';
        return false;
      }

      // Check if barcode already exists
      final existingProduct = await _databaseService.getProductByBarcode(barcode);
      if (existingProduct != null) {
        errorMessage.value = 'الباركود موجود مسبقاً';
        return false;
      }

      final product = ProductModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        description: description,
        barcode: barcode,
        category: category,
        price: price,
        cost: cost,
        quantity: quantity,
        minQuantity: minQuantity,
        unit: unit,
        imageUrl: imageUrl,
        isActive: true,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertProduct(product);

      // Sync with ERPNext if configured
      if (_apiService.isConfigured) {
        await _apiService.createItem({
          'item_code': barcode,
          'item_name': name,
          'description': description,
          'item_group': category,
          'standard_rate': price,
          'is_stock_item': 1,
        });
      }

      await loadProducts();
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة المنتج: $e';
      return false;
    }
  }

  Future<bool> updateProduct(ProductModel product) async {
    try {
      if (!_authController.hasPermission(UserPermissions.editProduct)) {
        errorMessage.value = 'ليس لديك صلاحية لتعديل المنتجات';
        return false;
      }

      final updatedProduct = product.copyWith(updatedAt: DateTime.now());
      await _databaseService.updateProduct(updatedProduct);

      // Sync with ERPNext if configured
      if (_apiService.isConfigured && product.erpData != null) {
        await _apiService.updateItem(product.id, {
          'item_name': product.name,
          'description': product.description,
          'standard_rate': product.price,
        });
      }

      await loadProducts();
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث المنتج: $e';
      return false;
    }
  }

  Future<bool> deleteProduct(String productId) async {
    try {
      if (!_authController.hasPermission(UserPermissions.deleteProduct)) {
        errorMessage.value = 'ليس لديك صلاحية لحذف المنتجات';
        return false;
      }

      await _databaseService.deleteProduct(productId);
      await loadProducts();
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف المنتج: $e';
      return false;
    }
  }

  Future<bool> updateProductQuantity(String productId, int newQuantity) async {
    try {
      await _databaseService.updateProductQuantity(productId, newQuantity);
      await loadProducts();
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث الكمية: $e';
      return false;
    }
  }

  ProductModel? getProductByBarcode(String barcode) {
    try {
      return products.firstWhere((product) => product.barcode == barcode);
    } catch (e) {
      return null;
    }
  }

  List<ProductModel> getLowStockProducts() {
    return products.where((product) => product.isLowStock).toList();
  }

  List<ProductModel> getProductsByCategory(String category) {
    return products.where((product) => product.category == category).toList();
  }

  double getTotalInventoryValue() {
    return products.fold(0, (sum, product) => sum + product.totalValue);
  }

  int getTotalProductCount() {
    return products.fold(0, (sum, product) => sum + product.quantity);
  }

  void clearSearch() {
    searchQuery.value = '';
    selectedCategory.value = 'all';
  }

  Future<void> refreshProducts() async {
    await loadProducts();
  }

  // Initialize some demo products
  Future<void> initializeDemoProducts() async {
    try {
      final existingProducts = await _databaseService.getAllProducts();
      if (existingProducts.isEmpty) {
        final demoProducts = [
          ProductModel(
            id: 'prod_001',
            name: 'كوكا كولا 330مل',
            description: 'مشروب غازي كوكا كولا علبة 330 مل',
            barcode: '1234567890123',
            category: ProductCategories.beverages,
            price: 2.50,
            cost: 1.75,
            quantity: 100,
            minQuantity: 20,
            unit: ProductUnits.piece,
            isActive: true,
            createdAt: DateTime.now(),
          ),
          ProductModel(
            id: 'prod_002',
            name: 'خبز أبيض',
            description: 'خبز أبيض طازج',
            barcode: '1234567890124',
            category: ProductCategories.food,
            price: 1.00,
            cost: 0.60,
            quantity: 50,
            minQuantity: 10,
            unit: ProductUnits.piece,
            isActive: true,
            createdAt: DateTime.now(),
          ),
          ProductModel(
            id: 'prod_003',
            name: 'حليب كامل الدسم 1 لتر',
            description: 'حليب طازج كامل الدسم 1 لتر',
            barcode: '1234567890125',
            category: ProductCategories.beverages,
            price: 4.50,
            cost: 3.20,
            quantity: 30,
            minQuantity: 5,
            unit: ProductUnits.liter,
            isActive: true,
            createdAt: DateTime.now(),
          ),
          ProductModel(
            id: 'prod_004',
            name: 'أرز بسمتي 1 كيلو',
            description: 'أرز بسمتي فاخر 1 كيلوجرام',
            barcode: '1234567890126',
            category: ProductCategories.food,
            price: 8.00,
            cost: 5.50,
            quantity: 25,
            minQuantity: 5,
            unit: ProductUnits.kg,
            isActive: true,
            createdAt: DateTime.now(),
          ),
          ProductModel(
            id: 'prod_005',
            name: 'شامبو للشعر',
            description: 'شامبو طبيعي للشعر 400 مل',
            barcode: '1234567890127',
            category: ProductCategories.health,
            price: 15.00,
            cost: 10.00,
            quantity: 15,
            minQuantity: 3,
            unit: ProductUnits.piece,
            isActive: true,
            createdAt: DateTime.now(),
          ),
        ];

        for (final product in demoProducts) {
          await _databaseService.insertProduct(product);
        }

        await loadProducts();
      }
    } catch (e) {
      print('Error initializing demo products: $e');
    }
  }
}
