import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/product_model.dart';
import '../models/invoice_model.dart';

class DatabaseService extends GetxService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'pos_app.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        phone TEXT,
        role TEXT NOT NULL,
        permissions TEXT NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        synced INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Products table
    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        barcode TEXT UNIQUE,
        category TEXT NOT NULL,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER NOT NULL DEFAULT 0,
        unit TEXT NOT NULL,
        image_url TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        erp_data TEXT,
        synced INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Invoices table
    await db.execute('''
      CREATE TABLE invoices (
        id TEXT PRIMARY KEY,
        invoice_number TEXT UNIQUE NOT NULL,
        customer_id TEXT,
        customer_name TEXT,
        customer_phone TEXT,
        cashier_id TEXT NOT NULL,
        cashier_name TEXT NOT NULL,
        subtotal REAL NOT NULL,
        tax_amount REAL NOT NULL DEFAULT 0,
        discount_amount REAL NOT NULL DEFAULT 0,
        total_amount REAL NOT NULL,
        paid_amount REAL NOT NULL,
        change_amount REAL NOT NULL DEFAULT 0,
        payment_method TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        erp_data TEXT,
        synced INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Invoice items table
    await db.execute('''
      CREATE TABLE invoice_items (
        id TEXT PRIMARY KEY,
        invoice_id TEXT NOT NULL,
        product_id TEXT NOT NULL,
        product_name TEXT NOT NULL,
        product_barcode TEXT,
        unit_price REAL NOT NULL,
        unit_cost REAL NOT NULL,
        quantity INTEGER NOT NULL,
        discount REAL NOT NULL DEFAULT 0,
        total_price REAL NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // Settings table
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_products_barcode ON products(barcode)');
    await db.execute('CREATE INDEX idx_products_category ON products(category)');
    await db.execute('CREATE INDEX idx_invoices_date ON invoices(created_at)');
    await db.execute('CREATE INDEX idx_invoices_cashier ON invoices(cashier_id)');
    await db.execute('CREATE INDEX idx_invoice_items_invoice ON invoice_items(invoice_id)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  // User operations
  Future<int> insertUser(UserModel user) async {
    final db = await database;
    final data = user.toJson();
    data['permissions'] = jsonEncode(user.permissions);
    data['is_active'] = user.isActive ? 1 : 0;
    data['synced'] = 0;
    
    return await db.insert('users', data, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<UserModel>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['permissions'] = jsonDecode(map['permissions']);
      map['is_active'] = map['is_active'] == 1;
      return UserModel.fromJson(map);
    });
  }

  Future<UserModel?> getUserById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['permissions'] = jsonDecode(map['permissions']);
      map['is_active'] = map['is_active'] == 1;
      return UserModel.fromJson(map);
    }
    return null;
  }

  Future<UserModel?> getUserByUsername(String username) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );
    
    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['permissions'] = jsonDecode(map['permissions']);
      map['is_active'] = map['is_active'] == 1;
      return UserModel.fromJson(map);
    }
    return null;
  }

  Future<int> updateUser(UserModel user) async {
    final db = await database;
    final data = user.toJson();
    data['permissions'] = jsonEncode(user.permissions);
    data['is_active'] = user.isActive ? 1 : 0;
    data['updated_at'] = DateTime.now().toIso8601String();
    
    return await db.update(
      'users',
      data,
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(String id) async {
    final db = await database;
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Product operations
  Future<int> insertProduct(ProductModel product) async {
    final db = await database;
    final data = product.toJson();
    data['is_active'] = product.isActive ? 1 : 0;
    data['erp_data'] = product.erpData != null ? jsonEncode(product.erpData) : null;
    data['synced'] = 0;
    
    return await db.insert('products', data, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<ProductModel>> getAllProducts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('products');
    
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['is_active'] = map['is_active'] == 1;
      if (map['erp_data'] != null) {
        map['erp_data'] = jsonDecode(map['erp_data']);
      }
      return ProductModel.fromJson(map);
    });
  }

  Future<ProductModel?> getProductById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['is_active'] = map['is_active'] == 1;
      if (map['erp_data'] != null) {
        map['erp_data'] = jsonDecode(map['erp_data']);
      }
      return ProductModel.fromJson(map);
    }
    return null;
  }

  Future<ProductModel?> getProductByBarcode(String barcode) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'barcode = ?',
      whereArgs: [barcode],
    );
    
    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['is_active'] = map['is_active'] == 1;
      if (map['erp_data'] != null) {
        map['erp_data'] = jsonDecode(map['erp_data']);
      }
      return ProductModel.fromJson(map);
    }
    return null;
  }

  Future<List<ProductModel>> searchProducts(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'name LIKE ? OR barcode LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );
    
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['is_active'] = map['is_active'] == 1;
      if (map['erp_data'] != null) {
        map['erp_data'] = jsonDecode(map['erp_data']);
      }
      return ProductModel.fromJson(map);
    });
  }

  Future<int> updateProduct(ProductModel product) async {
    final db = await database;
    final data = product.toJson();
    data['is_active'] = product.isActive ? 1 : 0;
    data['erp_data'] = product.erpData != null ? jsonEncode(product.erpData) : null;
    data['updated_at'] = DateTime.now().toIso8601String();
    
    return await db.update(
      'products',
      data,
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  Future<int> updateProductQuantity(String productId, int newQuantity) async {
    final db = await database;
    return await db.update(
      'products',
      {
        'quantity': newQuantity,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  Future<int> deleteProduct(String id) async {
    final db = await database;
    return await db.delete(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'settings',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    
    if (maps.isNotEmpty) {
      return maps.first['value'];
    }
    return null;
  }

  // Invoice operations
  Future<int> insertInvoice(InvoiceModel invoice) async {
    final db = await database;

    // Insert invoice
    final invoiceData = invoice.toJson();
    invoiceData['synced'] = 0;

    await db.insert('invoices', invoiceData, conflictAlgorithm: ConflictAlgorithm.replace);

    // Insert invoice items
    for (final item in invoice.items) {
      await db.insert('invoice_items', item.toJson(), conflictAlgorithm: ConflictAlgorithm.replace);
    }

    return 1;
  }

  Future<List<InvoiceModel>> getAllInvoices() async {
    final db = await database;

    // Get invoices
    final List<Map<String, dynamic>> invoiceMaps = await db.query('invoices', orderBy: 'created_at DESC');

    List<InvoiceModel> invoices = [];

    for (final invoiceMap in invoiceMaps) {
      // Get invoice items
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceMap['id']],
      );

      final items = itemMaps.map((itemMap) => InvoiceItemModel.fromJson(itemMap)).toList();

      final invoiceData = Map<String, dynamic>.from(invoiceMap);
      invoiceData['items'] = items.map((item) => item.toJson()).toList();

      invoices.add(InvoiceModel.fromJson(invoiceData));
    }

    return invoices;
  }

  Future<InvoiceModel?> getInvoiceById(String id) async {
    final db = await database;

    // Get invoice
    final List<Map<String, dynamic>> invoiceMaps = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (invoiceMaps.isEmpty) return null;

    // Get invoice items
    final List<Map<String, dynamic>> itemMaps = await db.query(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [id],
    );

    final items = itemMaps.map((itemMap) => InvoiceItemModel.fromJson(itemMap)).toList();

    final invoiceData = Map<String, dynamic>.from(invoiceMaps.first);
    invoiceData['items'] = items.map((item) => item.toJson()).toList();

    return InvoiceModel.fromJson(invoiceData);
  }

  Future<List<InvoiceModel>> getInvoicesByDateRange(DateTime startDate, DateTime endDate) async {
    final db = await database;

    final List<Map<String, dynamic>> invoiceMaps = await db.query(
      'invoices',
      where: 'created_at BETWEEN ? AND ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'created_at DESC',
    );

    List<InvoiceModel> invoices = [];

    for (final invoiceMap in invoiceMaps) {
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceMap['id']],
      );

      final items = itemMaps.map((itemMap) => InvoiceItemModel.fromJson(itemMap)).toList();

      final invoiceData = Map<String, dynamic>.from(invoiceMap);
      invoiceData['items'] = items.map((item) => item.toJson()).toList();

      invoices.add(InvoiceModel.fromJson(invoiceData));
    }

    return invoices;
  }

  Future<int> updateInvoice(InvoiceModel invoice) async {
    final db = await database;

    final data = invoice.toJson();
    data['updated_at'] = DateTime.now().toIso8601String();

    return await db.update(
      'invoices',
      data,
      where: 'id = ?',
      whereArgs: [invoice.id],
    );
  }

  Future<int> deleteInvoice(String id) async {
    final db = await database;

    // Delete invoice items first (due to foreign key)
    await db.delete(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [id],
    );

    // Delete invoice
    return await db.delete(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Sync operations
  Future<List<InvoiceModel>> getUnsyncedInvoices() async {
    final db = await database;

    final List<Map<String, dynamic>> invoiceMaps = await db.query(
      'invoices',
      where: 'synced = ?',
      whereArgs: [0],
      orderBy: 'created_at DESC',
    );

    List<InvoiceModel> invoices = [];

    for (final invoiceMap in invoiceMaps) {
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceMap['id']],
      );

      final items = itemMaps.map((itemMap) => InvoiceItemModel.fromJson(itemMap)).toList();

      final invoiceData = Map<String, dynamic>.from(invoiceMap);
      invoiceData['items'] = items.map((item) => item.toJson()).toList();

      invoices.add(InvoiceModel.fromJson(invoiceData));
    }

    return invoices;
  }

  Future<List<ProductModel>> getUnsyncedProducts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'synced = ?',
      whereArgs: [0],
    );

    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['is_active'] = map['is_active'] == 1;
      if (map['erp_data'] != null) {
        map['erp_data'] = jsonDecode(map['erp_data']);
      }
      return ProductModel.fromJson(map);
    });
  }

  Future<List<UserModel>> getUnsyncedUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'synced = ?',
      whereArgs: [0],
    );

    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['permissions'] = jsonDecode(map['permissions']);
      map['is_active'] = map['is_active'] == 1;
      return UserModel.fromJson(map);
    });
  }

  Future<int> markInvoiceAsSynced(String invoiceId, Map<String, dynamic>? erpData) async {
    final db = await database;
    return await db.update(
      'invoices',
      {
        'synced': 1,
        'erp_data': erpData != null ? jsonEncode(erpData) : null,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  Future<int> markProductAsSynced(String productId, Map<String, dynamic>? erpData) async {
    final db = await database;
    return await db.update(
      'products',
      {
        'synced': 1,
        'erp_data': erpData != null ? jsonEncode(erpData) : null,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  Future<int> markUserAsSynced(String userId) async {
    final db = await database;
    return await db.update(
      'users',
      {
        'synced': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  // Clear all data
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('users');
    await db.delete('products');
    await db.delete('invoices');
    await db.delete('invoice_items');
    await db.delete('settings');
  }
}
