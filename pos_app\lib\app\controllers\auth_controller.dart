import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/user_model.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';

class AuthController extends GetxController {
  final DatabaseService _databaseService = Get.find<DatabaseService>();
  final ApiService _apiService = Get.find<ApiService>();

  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Form fields for data binding
  final RxString username = ''.obs;
  final RxString password = ''.obs;
  final RxBool obscurePassword = true.obs;

  // Clear form fields
  void clearForm() {
    username.value = '';
    password.value = '';
    errorMessage.value = '';
  }

  // Toggle password visibility
  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  @override
  void onInit() {
    super.onInit();
    clearForm();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('current_user_id');
      
      if (userId != null) {
        final user = await _databaseService.getUserById(userId);
        if (user != null && user.isActive) {
          currentUser.value = user;
          isLoggedIn.value = true;
        } else {
          await logout();
        }
      }
    } catch (e) {
      print('Error checking login status: $e');
    }
  }

  Future<bool> login(String username, String password) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // Hash the password for future use
      // final hashedPassword = _hashPassword(password);

      // First try local authentication
      final user = await _databaseService.getUserByUsername(username);
      
      if (user != null && user.isActive) {
        // For demo purposes, we'll use a simple password check
        // In production, you should store hashed passwords
        if (await _verifyPassword(username, password)) {
          await _setCurrentUser(user);
          return true;
        }
      }

      // If local auth fails, try ERPNext authentication
      if (_apiService.isConfigured) {
        final erpResponse = await _apiService.login(username, password);
        if (erpResponse != null) {
          // Create or update local user from ERP data
          final erpUser = await _createUserFromERPData(erpResponse, username);
          if (erpUser != null) {
            await _setCurrentUser(erpUser);
            return true;
          }
        }
      }

      errorMessage.value = 'اسم المستخدم أو كلمة المرور غير صحيحة';
      return false;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تسجيل الدخول: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _setCurrentUser(UserModel user) async {
    currentUser.value = user;
    isLoggedIn.value = true;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user_id', user.id);
    await prefs.setString('last_login', DateTime.now().toIso8601String());
  }

  Future<UserModel?> _createUserFromERPData(Map<String, dynamic> erpData, String username) async {
    try {
      // Extract user data from ERP response
      final userData = erpData['user'] ?? {};
      
      final user = UserModel(
        id: userData['name'] ?? username,
        username: username,
        email: userData['email'] ?? '$<EMAIL>',
        fullName: userData['full_name'] ?? username,
        phone: userData['phone'] ?? '',
        role: _mapERPRoleToLocal(userData['role'] ?? 'cashier'),
        permissions: UserPermissions.rolePermissions[_mapERPRoleToLocal(userData['role'] ?? 'cashier')] ?? [],
        isActive: true,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertUser(user);
      return user;
    } catch (e) {
      print('Error creating user from ERP data: $e');
      return null;
    }
  }

  String _mapERPRoleToLocal(String erpRole) {
    switch (erpRole.toLowerCase()) {
      case 'system manager':
      case 'administrator':
        return UserRoles.admin;
      case 'sales manager':
      case 'accounts manager':
        return UserRoles.manager;
      case 'sales user':
        return UserRoles.cashier;
      default:
        return UserRoles.viewer;
    }
  }

  Future<bool> _verifyPassword(String username, String password) async {
    // For demo purposes, we'll use simple password verification
    // In production, implement proper password hashing and verification
    
    // Default passwords for demo users
    final defaultPasswords = {
      'admin': 'admin123',
      'manager': 'manager123',
      'cashier': 'cashier123',
    };

    return defaultPasswords[username] == password;
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<void> logout() async {
    try {
      // Logout from ERPNext if connected
      if (_apiService.isConfigured) {
        await _apiService.logout();
      }

      // Clear local session
      currentUser.value = null;
      isLoggedIn.value = false;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      await prefs.setString('last_logout', DateTime.now().toIso8601String());
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  bool hasPermission(String permission) {
    if (currentUser.value == null) return false;
    return currentUser.value!.permissions.contains(permission);
  }

  bool hasAnyPermission(List<String> permissions) {
    if (currentUser.value == null) return false;
    return permissions.any((permission) => currentUser.value!.permissions.contains(permission));
  }

  bool hasRole(String role) {
    if (currentUser.value == null) return false;
    return currentUser.value!.role == role;
  }

  bool get isAdmin => hasRole(UserRoles.admin);
  bool get isManager => hasRole(UserRoles.manager) || isAdmin;
  bool get isCashier => hasRole(UserRoles.cashier) || isManager;

  // User management methods (for admin/manager)
  Future<bool> createUser({
    required String username,
    required String email,
    required String fullName,
    required String phone,
    required String role,
    required String password,
  }) async {
    try {
      if (!hasPermission(UserPermissions.createUser)) {
        errorMessage.value = 'ليس لديك صلاحية لإنشاء مستخدمين';
        return false;
      }

      final user = UserModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        username: username,
        email: email,
        fullName: fullName,
        phone: phone,
        role: role,
        permissions: UserPermissions.rolePermissions[role] ?? [],
        isActive: true,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertUser(user);

      // Sync with ERPNext if configured
      if (_apiService.isConfigured) {
        await _apiService.createUser({
          'email': email,
          'first_name': fullName.split(' ').first,
          'last_name': fullName.split(' ').length > 1 ? fullName.split(' ').last : '',
          'username': username,
          'new_password': password,
          'enabled': 1,
        });
      }

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء المستخدم: $e';
      return false;
    }
  }

  Future<List<UserModel>> getAllUsers() async {
    try {
      if (!hasPermission(UserPermissions.viewUsers)) {
        return [];
      }
      return await _databaseService.getAllUsers();
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  Future<bool> updateUser(UserModel user) async {
    try {
      if (!hasPermission(UserPermissions.editUser)) {
        errorMessage.value = 'ليس لديك صلاحية لتعديل المستخدمين';
        return false;
      }

      await _databaseService.updateUser(user);

      // Update current user if it's the same user
      if (currentUser.value?.id == user.id) {
        currentUser.value = user;
      }

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث المستخدم: $e';
      return false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    try {
      if (!hasPermission(UserPermissions.deleteUser)) {
        errorMessage.value = 'ليس لديك صلاحية لحذف المستخدمين';
        return false;
      }

      if (currentUser.value?.id == userId) {
        errorMessage.value = 'لا يمكنك حذف حسابك الخاص';
        return false;
      }

      await _databaseService.deleteUser(userId);
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف المستخدم: $e';
      return false;
    }
  }

  Future<void> initializeDefaultUsers() async {
    try {
      final existingUsers = await _databaseService.getAllUsers();
      if (existingUsers.isEmpty) {
        // Create default admin user
        final adminUser = UserModel(
          id: 'admin_001',
          username: 'admin',
          email: '<EMAIL>',
          fullName: 'مدير النظام',
          phone: '1234567890',
          role: UserRoles.admin,
          permissions: UserPermissions.allPermissions,
          isActive: true,
          createdAt: DateTime.now(),
        );

        await _databaseService.insertUser(adminUser);

        // Create default manager user
        final managerUser = UserModel(
          id: 'manager_001',
          username: 'manager',
          email: '<EMAIL>',
          fullName: 'مدير المبيعات',
          phone: '1234567891',
          role: UserRoles.manager,
          permissions: UserPermissions.rolePermissions[UserRoles.manager]!,
          isActive: true,
          createdAt: DateTime.now(),
        );

        await _databaseService.insertUser(managerUser);

        // Create default cashier user
        final cashierUser = UserModel(
          id: 'cashier_001',
          username: 'cashier',
          email: '<EMAIL>',
          fullName: 'أمين الصندوق',
          phone: '1234567892',
          role: UserRoles.cashier,
          permissions: UserPermissions.rolePermissions[UserRoles.cashier]!,
          isActive: true,
          createdAt: DateTime.now(),
        );

        await _databaseService.insertUser(cashierUser);
      }
    } catch (e) {
      print('Error initializing default users: $e');
    }
  }
}
