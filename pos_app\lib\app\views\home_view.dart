import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/product_controller.dart';
import '../data/models/user_model.dart';
import '../routes/app_routes.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام نقاط البيع'),
        actions: [
          Obx(() => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  'مرحباً، ${authController.currentUser.value?.fullName ?? ''}',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.account_circle),
                  onSelected: (value) {
                    if (value == 'logout') {
                      _showLogoutDialog(context, authController);
                    } else if (value == 'settings') {
                      Get.toNamed(AppRoutes.settings);
                    } else if (value == 'debug') {
                      Get.toNamed(AppRoutes.debug);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          Icon(Icons.settings),
                          SizedBox(width: 8),
                          Text('الإعدادات'),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'debug',
                    child: Row(
                      children: [
                        Icon(Icons.bug_report),
                        SizedBox(width: 8),
                        Text('تشخيص'),
                      ],
                    ),
                  ),
                    const PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.logout),
                          SizedBox(width: 8),
                          Text('تسجيل الخروج'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome card
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: const Icon(
                        Icons.dashboard,
                        size: 30,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'لوحة التحكم الرئيسية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Obx(() => Text(
                            'الدور: ${_getRoleDisplayName(authController.currentUser.value?.role ?? '')}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Main menu grid
            const Text(
              'القائمة الرئيسية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(
              child: Obx(() => GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: _buildMenuItems(authController),
              )),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMenuItems(AuthController authController) {
    final List<MenuItemData> items = [];

    // POS - Available for cashiers and above
    if (authController.isCashier) {
      items.add(MenuItemData(
        title: 'نقاط البيع',
        icon: Icons.point_of_sale,
        color: Colors.green,
        route: AppRoutes.pos,
      ));
    }

    // Products - Available for all users
    items.add(MenuItemData(
      title: 'المنتجات',
      icon: Icons.inventory,
      color: Colors.orange,
      route: AppRoutes.products,
    ));

    // Invoices - Available for all users
    items.add(MenuItemData(
      title: 'الفواتير',
      icon: Icons.receipt_long,
      color: Colors.blue,
      route: AppRoutes.invoices,
    ));

    // Users - Available for managers and above
    if (authController.hasPermission(UserPermissions.viewUsers)) {
      items.add(MenuItemData(
        title: 'المستخدمين',
        icon: Icons.people,
        color: Colors.purple,
        route: AppRoutes.users,
      ));
    }

    // Reports - Available for managers and above
    if (authController.hasPermission(UserPermissions.viewReports)) {
      items.add(MenuItemData(
        title: 'التقارير',
        icon: Icons.analytics,
        color: Colors.teal,
        route: AppRoutes.reports,
      ));
    }

    // Settings - Available for admins
    if (authController.hasPermission(UserPermissions.manageSettings)) {
      items.add(MenuItemData(
        title: 'الإعدادات',
        icon: Icons.settings,
        color: Colors.grey,
        route: AppRoutes.settings,
      ));
    }

    return items.map((item) => _buildMenuItem(item)).toList();
  }

  Widget _buildMenuItem(MenuItemData item) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToRoute(item.route),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: item.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  item.icon,
                  size: 30,
                  color: item.color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                item.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case UserRoles.admin:
        return 'مدير النظام';
      case UserRoles.manager:
        return 'مدير';
      case UserRoles.cashier:
        return 'أمين صندوق';
      case UserRoles.viewer:
        return 'مشاهد';
      default:
        return 'غير محدد';
    }
  }

  void _navigateToRoute(String route) {
    try {
      // Special handling for products route
      if (route == AppRoutes.products) {
        // Ensure ProductController is initialized
        if (!Get.isRegistered<ProductController>()) {
          Get.put(ProductController());
        }

        // Initialize demo products if needed
        final productController = Get.find<ProductController>();
        productController.initializeDemoProducts();
      }

      Get.toNamed(route);
    } catch (e) {
      // Log navigation error
      // Fallback navigation
      Get.snackbar(
        'خطأ في التنقل',
        'حدث خطأ أثناء التنقل إلى الصفحة المطلوبة',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void _showLogoutDialog(BuildContext context, AuthController authController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              authController.logout();
              Get.offAllNamed(AppRoutes.login);
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}

class MenuItemData {
  final String title;
  final IconData icon;
  final Color color;
  final String route;

  MenuItemData({
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
  });
}
