import 'package:get/get.dart';
import '../controllers/reports_controller.dart';
import '../controllers/auth_controller.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';

/// Binding للتقارير - يدير تهيئة جميع التبعيات المطلوبة
class ReportsBinding extends Bindings {
  @override
  void dependencies() {
    // تهيئة الخدمات الأساسية إذا لم تكن موجودة
    _ensureBasicServices();
    
    // تهيئة ReportsController مع lazy loading
    Get.lazyPut<ReportsController>(
      () => ReportsController(),
      fenix: true, // يعيد إنشاء Controller إذا تم حذفه
    );
  }

  /// التأكد من وجود الخدمات الأساسية
  void _ensureBasicServices() {
    if (!Get.isRegistered<DatabaseService>()) {
      Get.put(DatabaseService(), permanent: true);
    }
    
    if (!Get.isRegistered<ApiService>()) {
      Get.put(ApiService(), permanent: true);
    }
    
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }
  }
}

/// Binding للمنتجات مع تهيئة كاملة
class ProductsBinding extends Bindings {
  @override
  void dependencies() {
    // تهيئة ProductController مع جميع التبعيات
    Get.lazyPut<ProductController>(
      () => ProductController(),
      fenix: true,
    );
    
    // يمكن إضافة controllers إضافية للمنتجات
    Get.lazyPut<CategoryController>(
      () => CategoryController(),
      fenix: true,
    );
  }
}

/// Controller للفئات (مثال)
class CategoryController extends GetxController {
  final RxList<String> categories = <String>[].obs;
  
  @override
  void onInit() {
    super.onInit();
    loadCategories();
  }
  
  void loadCategories() {
    categories.value = [
      'طعام',
      'مشروبات', 
      'إلكترونيات',
      'ملابس',
      'صحة',
    ];
  }
}
