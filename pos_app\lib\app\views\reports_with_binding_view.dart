import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/reports_controller.dart';

class ReportsView extends GetView<ReportsController> {
  const ReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _showDateRangePicker(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshReports(),
          ),
          Obx(() => IconButton(
            icon: controller.isLoading.value 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.download),
            onPressed: controller.isLoading.value 
                ? null 
                : () => controller.exportReport(),
          )),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.invoices.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.errorMessage.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage.value,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.loadReports(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateRangeCard(),
              const SizedBox(height: 16),
              _buildSummaryCards(),
              const SizedBox(height: 16),
              _buildChartsSection(),
              const SizedBox(height: 16),
              _buildTopProductsSection(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildDateRangeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.date_range, color: Colors.blue),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'النطاق الزمني',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Obx(() => Text(
                    '${_formatDate(controller.startDate.value)} - ${_formatDate(controller.endDate.value)}',
                    style: TextStyle(color: Colors.grey[600]),
                  )),
                ],
              ),
            ),
            TextButton(
              onPressed: () => _showDateRangePicker(Get.context!),
              child: const Text('تغيير'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(child: _buildSummaryCard(
          'إجمالي المبيعات',
          () => '${controller.totalSales.value.toStringAsFixed(2)} ر.س',
          Icons.attach_money,
          Colors.green,
        )),
        const SizedBox(width: 8),
        Expanded(child: _buildSummaryCard(
          'عدد الفواتير',
          () => '${controller.totalInvoices.value}',
          Icons.receipt,
          Colors.blue,
        )),
        const SizedBox(width: 8),
        Expanded(child: _buildSummaryCard(
          'الربح المقدر',
          () => '${controller.totalProfit.value.toStringAsFixed(2)} ر.س',
          Icons.trending_up,
          Colors.orange,
        )),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String Function() getValue, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Obx(() => Text(
              getValue(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المبيعات حسب طريقة الدفع',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Obx(() {
              if (controller.salesByPaymentMethod.isEmpty) {
                return const Center(
                  child: Text('لا توجد بيانات'),
                );
              }
              
              return Column(
                children: controller.salesByPaymentMethod.entries.map((entry) {
                  final percentage = (entry.value / controller.totalInvoices.value * 100);
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(entry.key),
                        ),
                        Expanded(
                          flex: 3,
                          child: LinearProgressIndicator(
                            value: percentage / 100,
                            backgroundColor: Colors.grey[300],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('${percentage.toStringAsFixed(1)}%'),
                      ],
                    ),
                  );
                }).toList(),
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildTopProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أفضل المنتجات مبيعاً',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children: controller.getTopSellingProducts().map((product) {
              return ListTile(
                leading: const CircleAvatar(
                  child: Icon(Icons.inventory),
                ),
                title: Text(product['name']),
                subtitle: Text('الكمية: ${product['quantity']}'),
                trailing: Text(
                  '${product['revenue'].toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _showDateRangePicker(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: controller.startDate.value,
        end: controller.endDate.value,
      ),
    );

    if (picked != null) {
      controller.setDateRange(picked.start, picked.end);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
