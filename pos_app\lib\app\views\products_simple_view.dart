import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/product_controller.dart';
import '../controllers/auth_controller.dart';
import '../data/models/product_model.dart';
import '../data/models/user_model.dart';

class ProductsView extends GetView<ProductController> {
  const ProductsView({super.key});

  AuthController get authController => Get.find<AuthController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنتجات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshProducts(),
          ),
          if (authController.hasPermission(UserPermissions.createProduct))
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddProductDialog(context),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'البحث في المنتجات',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => controller.searchProducts(value),
            ),
          ),

          // Category filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Obx(() => ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildCategoryChip('all', 'الكل'),
                ...ProductCategories.allCategories.map(
                  (category) => _buildCategoryChip(
                    category,
                    ProductCategories.categoryNames[category] ?? category,
                  ),
                ),
              ],
            )),
          ),

          // Products list
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.filteredProducts.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.inventory, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('لا توجد منتجات'),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: controller.filteredProducts.length,
                itemBuilder: (context, index) {
                  final product = controller.filteredProducts[index];
                  return _buildProductCard(context, product);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: authController.hasPermission(UserPermissions.createProduct)
          ? FloatingActionButton(
              onPressed: () => _showAddProductDialog(context),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildCategoryChip(String category, String label) {
    return Obx(() => Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: controller.selectedCategory.value == category,
        onSelected: (selected) => controller.filterByCategory(category),
      ),
    ));
  }

  Widget _buildProductCard(BuildContext context, ProductModel product) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.inventory, color: Colors.grey),
        ),
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(product.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  'السعر: ${product.price.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'المخزون: ${product.quantity}',
                  style: TextStyle(
                    color: product.isLowStock ? Colors.red : Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (product.isLowStock)
              const Text(
                'تحذير: المخزون منخفض',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleProductAction(context, value, product),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            if (authController.hasPermission(UserPermissions.editProduct))
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
            if (authController.hasPermission(UserPermissions.deleteProduct))
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _handleProductAction(BuildContext context, String action, ProductModel product) {
    switch (action) {
      case 'view':
        _showProductDetails(context, product);
        break;
      case 'edit':
        _showEditProductDialog(context, product);
        break;
      case 'delete':
        _showDeleteConfirmation(context, product);
        break;
    }
  }

  void _showAddProductDialog(BuildContext context) {
    _showMessage(context, 'إضافة منتج جديد - قريباً');
  }

  void _showEditProductDialog(BuildContext context, ProductModel product) {
    _showMessage(context, 'تعديل ${product.name} - قريباً');
  }

  void _showProductDetails(BuildContext context, ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(product.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الوصف:', product.description),
            _buildDetailRow('الباركود:', product.barcode),
            _buildDetailRow('الفئة:', ProductCategories.categoryNames[product.category] ?? product.category),
            _buildDetailRow('السعر:', '${product.price.toStringAsFixed(2)} ر.س'),
            _buildDetailRow('التكلفة:', '${product.cost.toStringAsFixed(2)} ر.س'),
            _buildDetailRow('الربح:', '${product.profit.toStringAsFixed(2)} ر.س'),
            _buildDetailRow('الكمية:', '${product.quantity}'),
            _buildDetailRow('الحد الأدنى:', '${product.minQuantity}'),
            if (product.isLowStock)
              const Text(
                'تحذير: المخزون منخفض',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من رغبتك في حذف "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final success = await controller.deleteProduct(product.id);
              if (context.mounted) {
                Navigator.of(context).pop();
                if (success) {
                  _showMessage(context, 'تم حذف المنتج بنجاح');
                } else {
                  _showMessage(context, controller.errorMessage.value);
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
