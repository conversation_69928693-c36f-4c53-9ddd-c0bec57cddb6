import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService extends getx.GetxService {
  late Dio _dio;
  String? _baseUrl;
  String? _apiKey;
  String? _apiSecret;

  @override
  void onInit() {
    super.onInit();
    _initializeDio();
    _loadSettings();
  }

  void _initializeDio() {
    _dio = Dio();
    
    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add authentication headers
          if (_apiKey != null && _apiSecret != null) {
            final credentials = base64Encode(utf8.encode('$_apiKey:$_apiSecret'));
            options.headers['Authorization'] = 'Basic $credentials';
          }
          
          // Add content type
          options.headers['Content-Type'] = 'application/json';
          options.headers['Accept'] = 'application/json';
          
          print('REQUEST: ${options.method} ${options.uri}');
          print('HEADERS: ${options.headers}');
          print('DATA: ${options.data}');
          
          handler.next(options);
        },
        onResponse: (response, handler) {
          print('RESPONSE: ${response.statusCode} ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          print('ERROR: ${error.message}');
          print('ERROR DATA: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _baseUrl = prefs.getString('erp_base_url');
    _apiKey = prefs.getString('erp_api_key');
    _apiSecret = prefs.getString('erp_api_secret');
  }

  Future<void> configureERPNext({
    required String baseUrl,
    required String apiKey,
    required String apiSecret,
  }) async {
    _baseUrl = baseUrl.endsWith('/') ? baseUrl : '$baseUrl/';
    _apiKey = apiKey;
    _apiSecret = apiSecret;

    // Save settings
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('erp_base_url', _baseUrl!);
    await prefs.setString('erp_api_key', _apiKey!);
    await prefs.setString('erp_api_secret', _apiSecret!);
  }

  bool get isConfigured => _baseUrl != null && _apiKey != null && _apiSecret != null;

  String get baseUrl => _baseUrl ?? '';

  // Generic API methods
  Future<Response> get(String endpoint, {Map<String, dynamic>? queryParameters}) async {
    if (!isConfigured) throw Exception('ERPNext not configured');
    
    return await _dio.get(
      '$_baseUrl$endpoint',
      queryParameters: queryParameters,
    );
  }

  Future<Response> post(String endpoint, {dynamic data}) async {
    if (!isConfigured) throw Exception('ERPNext not configured');
    
    return await _dio.post(
      '$_baseUrl$endpoint',
      data: data,
    );
  }

  Future<Response> put(String endpoint, {dynamic data}) async {
    if (!isConfigured) throw Exception('ERPNext not configured');
    
    return await _dio.put(
      '$_baseUrl$endpoint',
      data: data,
    );
  }

  Future<Response> delete(String endpoint) async {
    if (!isConfigured) throw Exception('ERPNext not configured');
    
    return await _dio.delete('$_baseUrl$endpoint');
  }

  // Test connection
  Future<bool> testConnection() async {
    try {
      if (!isConfigured) return false;
      
      final response = await get('api/method/frappe.auth.get_logged_user');
      return response.statusCode == 200;
    } catch (e) {
      print('Connection test failed: $e');
      return false;
    }
  }

  // User authentication
  Future<Map<String, dynamic>?> login(String username, String password) async {
    try {
      final response = await post('api/method/login', data: {
        'usr': username,
        'pwd': password,
      });

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Login failed: $e');
      return null;
    }
  }

  Future<bool> logout() async {
    try {
      final response = await post('api/method/logout');
      return response.statusCode == 200;
    } catch (e) {
      print('Logout failed: $e');
      return false;
    }
  }

  // User management
  Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      final response = await get('api/resource/User', queryParameters: {
        'fields': '["name","email","full_name","phone","enabled","creation"]',
        'filters': '[["User","enabled","=",1]]',
      });

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data'] ?? []);
      }
      return [];
    } catch (e) {
      print('Get users failed: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> createUser(Map<String, dynamic> userData) async {
    try {
      final response = await post('api/resource/User', data: userData);
      
      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Create user failed: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> updateUser(String userId, Map<String, dynamic> userData) async {
    try {
      final response = await put('api/resource/User/$userId', data: userData);
      
      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Update user failed: $e');
      return null;
    }
  }

  // Product management
  Future<List<Map<String, dynamic>>> getItems() async {
    try {
      final response = await get('api/resource/Item', queryParameters: {
        'fields': '["name","item_name","description","item_code","item_group","standard_rate","creation","modified"]',
        'filters': '[["Item","disabled","=",0]]',
      });

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data'] ?? []);
      }
      return [];
    } catch (e) {
      print('Get items failed: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> createItem(Map<String, dynamic> itemData) async {
    try {
      final response = await post('api/resource/Item', data: itemData);
      
      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Create item failed: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> updateItem(String itemId, Map<String, dynamic> itemData) async {
    try {
      final response = await put('api/resource/Item/$itemId', data: itemData);
      
      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Update item failed: $e');
      return null;
    }
  }

  // Sales Invoice management
  Future<List<Map<String, dynamic>>> getSalesInvoices() async {
    try {
      final response = await get('api/resource/Sales Invoice', queryParameters: {
        'fields': '["name","customer","posting_date","grand_total","status","creation"]',
        'limit_page_length': '100',
      });

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data'] ?? []);
      }
      return [];
    } catch (e) {
      print('Get sales invoices failed: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> createSalesInvoice(Map<String, dynamic> invoiceData) async {
    try {
      final response = await post('api/resource/Sales Invoice', data: invoiceData);
      
      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Create sales invoice failed: $e');
      return null;
    }
  }

  // Stock management
  Future<List<Map<String, dynamic>>> getStockLevels() async {
    try {
      final response = await get('api/resource/Bin', queryParameters: {
        'fields': '["item_code","warehouse","actual_qty","reserved_qty","projected_qty"]',
      });

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data'] ?? []);
      }
      return [];
    } catch (e) {
      print('Get stock levels failed: $e');
      return [];
    }
  }

  // Sync methods
  Future<void> syncAllData() async {
    try {
      // This method would implement full data synchronization
      // between the POS app and ERPNext
      print('Starting full data sync...');
      
      // Sync users, items, and other data
      await Future.wait([
        getUsers(),
        getItems(),
        getSalesInvoices(),
        getStockLevels(),
      ]);
      
      print('Data sync completed');
    } catch (e) {
      print('Data sync failed: $e');
      rethrow;
    }
  }
}
