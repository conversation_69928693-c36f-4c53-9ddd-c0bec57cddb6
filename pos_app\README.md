# نظام نقاط البيع - Point of Sale System

تطبيق نقاط البيع متكامل مبني باستخدام Flutter و GetX مع إمكانية الربط مع ERPNext.

## المميزات الرئيسية

### 🔐 نظام المصادقة وإدارة المستخدمين
- تسجيل دخول آمن مع تشفير كلمات المرور
- أربعة أدوار مختلفة: مدير النظام، مدير، أمين صندوق، مشاهد
- نظام صلاحيات شامل لكل دور
- إمكانية إنشاء وتعديل وحذف المستخدمين

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات حسب الفئات
- إدارة المخزون والكميات
- دعم الباركود
- تتبع التكلفة والربح

### 🧾 نظام الفواتير
- إنشاء فواتير البيع
- طرق دفع متعددة (نقدي، بطاقة، محفظة إلكترونية)
- حساب الضرائب والخصومات
- طباعة الفواتير

### 🔗 التكامل مع ERPNext
- مزامنة البيانات مع ERPNext
- استيراد المنتجات والمستخدمين
- رفع الفواتير تلقائياً
- إعدادات مرنة للاتصال

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تحليل الأرباح
- تقارير المخزون
- إحصائيات الأداء

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **GetX**: إدارة الحالة والتوجيه
- **SQLite**: قاعدة البيانات المحلية
- **Dio**: طلبات HTTP
- **SharedPreferences**: تخزين الإعدادات
- **Crypto**: تشفير البيانات

## بنية المشروع

```
lib/
├── app/
│   ├── controllers/          # تحكم في الحالة
│   │   └── auth_controller.dart
│   ├── data/
│   │   ├── models/          # نماذج البيانات
│   │   │   ├── user_model.dart
│   │   │   ├── product_model.dart
│   │   │   └── invoice_model.dart
│   │   └── services/        # الخدمات
│   │       ├── api_service.dart
│   │       └── database_service.dart
│   ├── routes/              # التوجيه
│   │   ├── app_pages.dart
│   │   └── app_routes.dart
│   └── views/               # واجهات المستخدم
│       ├── splash_view.dart
│       ├── login_view.dart
│       ├── home_view.dart
│       ├── pos_view.dart
│       ├── products_view.dart
│       ├── users_view.dart
│       ├── invoices_view.dart
│       ├── reports_view.dart
│       └── settings_view.dart
└── main.dart
```

## التثبيت والتشغيل

### المتطلبات
- Flutter SDK (3.5.4 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- Git

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd pos_app
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
# للويب
flutter run -d chrome

# للأندرويد
flutter run -d android

# للويندوز (يتطلب تفعيل Developer Mode)
flutter run -d windows
```

## بيانات تجريبية

يحتوي التطبيق على مستخدمين تجريبيين:

| الدور | اسم المستخدم | كلمة المرور |
|-------|-------------|-------------|
| مدير النظام | admin | admin123 |
| مدير المبيعات | manager | manager123 |
| أمين الصندوق | cashier | cashier123 |

## إعداد ERPNext

لربط التطبيق مع ERPNext:

1. **إنشاء API Key في ERPNext**
   - اذهب إلى User > API Secret
   - أنشئ مفتاح API جديد

2. **إعداد الاتصال في التطبيق**
   - اذهب إلى الإعدادات
   - أدخل رابط ERPNext
   - أدخل API Key و API Secret

3. **اختبار الاتصال**
   - استخدم زر "اختبار الاتصال"
   - تأكد من نجاح الاتصال

## الصلاحيات والأدوار

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين
- إعدادات النظام
- مزامنة البيانات

### مدير المبيعات (Manager)
- إدارة المنتجات
- إنشاء وتعديل الفواتير
- عرض التقارير
- إدارة الإعدادات

### أمين الصندوق (Cashier)
- عرض المنتجات
- إنشاء الفواتير
- عرض الفواتير

### مشاهد (Viewer)
- عرض البيانات فقط
- لا يمكن التعديل

## ملاحظات مهمة

1. **Developer Mode**: لتشغيل التطبيق على Windows، يجب تفعيل Developer Mode
2. **الأمان**: تأكد من تشفير البيانات الحساسة
3. **النسخ الاحتياطي**: قم بعمل نسخ احتياطية دورية من قاعدة البيانات
4. **التحديثات**: تابع التحديثات الأمنية للمكتبات المستخدمة

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح Issue في GitHub.
