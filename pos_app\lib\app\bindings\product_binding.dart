import 'package:get/get.dart';
import '../controllers/product_controller.dart';
import '../controllers/auth_controller.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';

class ProductBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure dependencies are available
    if (!Get.isRegistered<DatabaseService>()) {
      Get.put(DatabaseService());
    }
    
    if (!Get.isRegistered<ApiService>()) {
      Get.put(ApiService());
    }
    
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController());
    }
    
    // Put ProductController
    Get.put(ProductController());
  }
}
