<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="notification_action_color_filter">@color/androidx_core_secondary_text_default_material_light</color>
    <dimen name="notification_content_margin_start">0dp</dimen>
    <dimen name="notification_main_column_padding_top">0dp</dimen>
    <dimen name="notification_media_narrow_margin">12dp</dimen>
    <dimen name="preference_dropdown_padding_start">72dp</dimen>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance.Material"/>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="android:TextAppearance.Material.Body1"/>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="android:TextAppearance.Material.Body2"/>
    <style name="Base.TextAppearance.AppCompat.Button" parent="android:TextAppearance.Material.Button"/>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="android:TextAppearance.Material.Caption"/>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="android:TextAppearance.Material.Display1"/>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="android:TextAppearance.Material.Display2"/>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="android:TextAppearance.Material.Display3"/>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="android:TextAppearance.Material.Display4"/>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="android:TextAppearance.Material.Headline"/>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="android:TextAppearance.Material.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Large" parent="android:TextAppearance.Material.Large"/>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="android:TextAppearance.Material.Large.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="android:TextAppearance.Material.Medium"/>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="android:TextAppearance.Material.Medium.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="android:TextAppearance.Material.Menu"/>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="android:TextAppearance.Material.SearchResult.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="android:TextAppearance.Material.SearchResult.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="android:TextAppearance.Material.Small"/>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="android:TextAppearance.Material.Small.Inverse"/>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="android:TextAppearance.Material.Subhead"/>
    <style name="Base.TextAppearance.AppCompat.Title" parent="android:TextAppearance.Material.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="android:TextAppearance.Material.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="android:TextAppearance.Material.Widget.ActionMode.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="android:TextAppearance.Material.Widget.ActionMode.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="android:TextAppearance.Material.Widget.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="android:TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="android:TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="android:TextAppearance.Material.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="android:TextAppearance.Material.Widget.TextView.SpinnerItem"/>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="android:TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="android:TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V21.Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V21.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V21.Theme.AppCompat.Light"/>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V21.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V21.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.V21.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
        
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>

        
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>

        
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>

        
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>

        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="android:Widget.Material.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="android:Widget.Material.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="android:Widget.Material.ActionButton">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="android:Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="android:Widget.Material.ActionButton.Overflow">
        <item name="android:src">@null</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.Material.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget.Material.Button"/>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="android:Widget.Material.Button.Borderless"/>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="android:Widget.Material.Button.Borderless.Colored">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="android:Widget.Material.Button.Small"/>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget.Material.ButtonBar"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.Material.CompoundButton.CheckBox"/>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.Material.CompoundButton.RadioButton"/>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="android:Widget.Material.DropDownItem.Spinner"/>
    <style name="Base.Widget.AppCompat.EditText" parent="android:Widget.Material.EditText">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.Material.ImageButton"/>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="android:Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="android:Widget.Material.Light.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="android:Widget.Material.Light.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="android:Widget.Material.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.Material.ListView"/>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="android:Widget.Material.ListView.DropDown"/>
    <style name="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="android:Widget.Material.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Material.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Material.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.Material.RatingBar"/>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget.Material.SeekBar"/>
    <style name="Base.Widget.AppCompat.Spinner" parent="android:Widget.Material.Spinner"/>
    <style name="Base.Widget.AppCompat.TextView" parent="android:Widget.Material.TextView"/>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.Material.TextView.SpinnerItem"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget.Material.Toolbar.Button.Navigation">
    </style>
    <style name="Platform.AppCompat" parent="Platform.V21.AppCompat"/>
    <style name="Platform.AppCompat.Light" parent="Platform.V21.AppCompat.Light"/>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
        
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark"/>
    <style name="Platform.ThemeOverlay.AppCompat.Light"/>
    <style name="Platform.V21.AppCompat" parent="android:Theme.Material.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style>
    <style name="Platform.V21.AppCompat.Light" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>

        
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    </style>
    <style name="PreferenceSummaryTextStyle">
        <item name="android:textAppearance">?android:attr/textAppearanceListItemSecondary</item>
    </style>
    <style name="PreferenceThemeOverlay" parent="BasePreferenceThemeOverlay">
        <item name="preferenceCategoryTitleTextColor">?android:attr/colorAccent</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification"/>
    <style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title"/>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
    </style>
</resources>