import 'package:get/get.dart';
import '../data/models/invoice_model.dart';
import '../data/models/product_model.dart';
import '../data/models/user_model.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';
import 'auth_controller.dart';
import 'product_controller.dart';

class InvoiceController extends GetxController {
  final DatabaseService _databaseService = Get.find<DatabaseService>();
  final ApiService _apiService = Get.find<ApiService>();
  final AuthController _authController = Get.find<AuthController>();
  final ProductController _productController = Get.find<ProductController>();

  final RxList<InvoiceModel> invoices = <InvoiceModel>[].obs;
  final RxList<InvoiceModel> filteredInvoices = <InvoiceModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;
  final RxString selectedStatus = 'all'.obs;

  // Current invoice being created
  final RxList<InvoiceItemModel> currentInvoiceItems = <InvoiceItemModel>[].obs;
  final RxString customerName = ''.obs;
  final RxString customerPhone = ''.obs;
  final RxDouble subtotal = 0.0.obs;
  final RxDouble taxAmount = 0.0.obs;
  final RxDouble discountAmount = 0.0.obs;
  final RxDouble totalAmount = 0.0.obs;
  final RxString paymentMethod = PaymentMethods.cash.obs;

  // Tax rate (can be configurable)
  final double taxRate = 0.15; // 15% VAT

  @override
  void onInit() {
    super.onInit();
    loadInvoices();
    
    // Listen to search query changes
    debounce(searchQuery, (_) => _filterInvoices(), time: const Duration(milliseconds: 500));
    ever(selectedStatus, (_) => _filterInvoices());
    
    // Listen to current invoice changes
    ever(currentInvoiceItems, (_) => _calculateTotals());
    ever(discountAmount, (_) => _calculateTotals());
  }

  Future<void> loadInvoices() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // Load invoices from local database
      // Note: We need to implement getInvoices in DatabaseService
      // For now, we'll use an empty list
      invoices.value = [];
      _filterInvoices();

      // Sync with ERPNext if configured
      if (_apiService.isConfigured) {
        await syncWithERP();
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الفواتير: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> syncWithERP() async {
    try {
      if (!_apiService.isConfigured) return;

      final erpInvoices = await _apiService.getSalesInvoices();
      
      // Process ERP invoices and save to local database
      // Implementation depends on ERP data structure
      
    } catch (e) {
      print('Error syncing invoices with ERP: $e');
    }
  }

  void _filterInvoices() {
    var filtered = invoices.where((invoice) {
      final matchesSearch = searchQuery.value.isEmpty ||
          invoice.invoiceNumber.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
          invoice.customerName.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
          invoice.customerPhone.contains(searchQuery.value);

      final matchesStatus = selectedStatus.value == 'all' ||
          invoice.status == selectedStatus.value;

      return matchesSearch && matchesStatus;
    }).toList();

    filteredInvoices.value = filtered;
  }

  void _calculateTotals() {
    double sub = currentInvoiceItems.fold(0, (sum, item) => sum + item.totalPrice);
    subtotal.value = sub;
    
    double tax = sub * taxRate;
    taxAmount.value = tax;
    
    double total = sub + tax - discountAmount.value;
    totalAmount.value = total > 0 ? total : 0;
  }

  void addItemToInvoice(ProductModel product, int quantity) {
    if (quantity <= 0) return;
    
    // Check if product already exists in current invoice
    final existingItemIndex = currentInvoiceItems.indexWhere(
      (item) => item.productId == product.id
    );

    if (existingItemIndex != -1) {
      // Update existing item quantity
      final existingItem = currentInvoiceItems[existingItemIndex];
      final newQuantity = existingItem.quantity + quantity;
      final newTotalPrice = product.price * newQuantity;
      
      final updatedItem = existingItem.copyWith(
        quantity: newQuantity,
        totalPrice: newTotalPrice,
      );
      
      currentInvoiceItems[existingItemIndex] = updatedItem;
    } else {
      // Add new item
      final item = InvoiceItemModel.fromProduct(product, quantity);
      currentInvoiceItems.add(item);
    }
  }

  void removeItemFromInvoice(String itemId) {
    currentInvoiceItems.removeWhere((item) => item.id == itemId);
  }

  void updateItemQuantity(String itemId, int newQuantity) {
    if (newQuantity <= 0) {
      removeItemFromInvoice(itemId);
      return;
    }

    final itemIndex = currentInvoiceItems.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1) {
      final item = currentInvoiceItems[itemIndex];
      final newTotalPrice = item.unitPrice * newQuantity;
      
      final updatedItem = item.copyWith(
        quantity: newQuantity,
        totalPrice: newTotalPrice,
      );
      
      currentInvoiceItems[itemIndex] = updatedItem;
    }
  }

  void updateItemDiscount(String itemId, double discount) {
    final itemIndex = currentInvoiceItems.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1) {
      final item = currentInvoiceItems[itemIndex];
      final updatedItem = item.copyWith(discount: discount);
      currentInvoiceItems[itemIndex] = updatedItem;
    }
  }

  void clearCurrentInvoice() {
    currentInvoiceItems.clear();
    customerName.value = '';
    customerPhone.value = '';
    discountAmount.value = 0.0;
    paymentMethod.value = PaymentMethods.cash;
  }

  Future<String?> createInvoice({
    required double paidAmount,
    String? customerId,
  }) async {
    try {
      if (!_authController.hasPermission(UserPermissions.createInvoice)) {
        errorMessage.value = 'ليس لديك صلاحية لإنشاء فواتير';
        return null;
      }

      if (currentInvoiceItems.isEmpty) {
        errorMessage.value = 'لا يمكن إنشاء فاتورة فارغة';
        return null;
      }

      if (paidAmount < totalAmount.value) {
        errorMessage.value = 'المبلغ المدفوع أقل من إجمالي الفاتورة';
        return null;
      }

      final invoiceNumber = _generateInvoiceNumber();
      final changeAmount = paidAmount - totalAmount.value;

      final invoice = InvoiceModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        invoiceNumber: invoiceNumber,
        customerId: customerId ?? '',
        customerName: customerName.value,
        customerPhone: customerPhone.value,
        cashierId: _authController.currentUser.value!.id,
        cashierName: _authController.currentUser.value!.fullName,
        items: List.from(currentInvoiceItems),
        subtotal: subtotal.value,
        taxAmount: taxAmount.value,
        discountAmount: discountAmount.value,
        totalAmount: totalAmount.value,
        paidAmount: paidAmount,
        changeAmount: changeAmount,
        paymentMethod: paymentMethod.value,
        status: InvoiceStatus.completed,
        createdAt: DateTime.now(),
      );

      // Save invoice to local database
      // Note: We need to implement insertInvoice in DatabaseService
      
      // Update product quantities
      for (final item in currentInvoiceItems) {
        final product = _productController.products.firstWhere(
          (p) => p.id == item.productId
        );
        final newQuantity = product.quantity - item.quantity;
        await _productController.updateProductQuantity(item.productId, newQuantity);
      }

      // Sync with ERPNext if configured
      if (_apiService.isConfigured) {
        await _syncInvoiceWithERP(invoice);
      }

      // Clear current invoice
      clearCurrentInvoice();

      // Reload invoices
      await loadInvoices();

      return invoiceNumber;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء الفاتورة: $e';
      return null;
    }
  }

  Future<void> _syncInvoiceWithERP(InvoiceModel invoice) async {
    try {
      final erpInvoiceData = {
        'customer': invoice.customerName.isNotEmpty ? invoice.customerName : 'Walk-in Customer',
        'posting_date': invoice.createdAt.toIso8601String().split('T')[0],
        'items': invoice.items.map((item) => {
          'item_code': item.productBarcode,
          'item_name': item.productName,
          'qty': item.quantity,
          'rate': item.unitPrice,
          'amount': item.totalPrice,
        }).toList(),
        'taxes_and_charges_template': 'VAT 15%',
        'payment_terms_template': 'Immediate Payment',
      };

      await _apiService.createSalesInvoice(erpInvoiceData);
    } catch (e) {
      print('Error syncing invoice with ERP: $e');
    }
  }

  String _generateInvoiceNumber() {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    return 'INV-$dateStr-$timeStr';
  }

  void searchInvoices(String query) {
    searchQuery.value = query;
  }

  void filterByStatus(String status) {
    selectedStatus.value = status;
  }

  void setCustomerInfo(String name, String phone) {
    customerName.value = name;
    customerPhone.value = phone;
  }

  void setPaymentMethod(String method) {
    paymentMethod.value = method;
  }

  void setDiscount(double discount) {
    discountAmount.value = discount;
  }

  // Quick add product by barcode
  bool addProductByBarcode(String barcode, {int quantity = 1}) {
    final product = _productController.getProductByBarcode(barcode);
    if (product != null) {
      if (product.quantity >= quantity) {
        addItemToInvoice(product, quantity);
        return true;
      } else {
        errorMessage.value = 'الكمية المتاحة غير كافية';
        return false;
      }
    } else {
      errorMessage.value = 'المنتج غير موجود';
      return false;
    }
  }

  // Get invoice statistics
  Map<String, dynamic> getInvoiceStats() {
    final today = DateTime.now();
    final todayInvoices = invoices.where((invoice) =>
      invoice.createdAt.year == today.year &&
      invoice.createdAt.month == today.month &&
      invoice.createdAt.day == today.day
    ).toList();

    final todaySales = todayInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
    final todayProfit = todayInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalProfit);

    return {
      'todayInvoices': todayInvoices.length,
      'todaySales': todaySales,
      'todayProfit': todayProfit,
      'totalInvoices': invoices.length,
      'totalSales': invoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount),
    };
  }

  Future<void> refreshInvoices() async {
    await loadInvoices();
  }

  void clearSearch() {
    searchQuery.value = '';
    selectedStatus.value = 'all';
  }
}
