# دليل الإعداد - Setup Guide

## متطلبات النظام

### Flutter SDK
- الإصدار المطلوب: 3.5.4 أو أحدث
- تحميل من: https://flutter.dev/docs/get-started/install

### أدوات التطوير
- **Android Studio** (للتطوير على Android)
- **VS Code** مع إضافات Flutter و Dart
- **Git** لإدارة الإصدارات

## خطوات الإعداد التفصيلية

### 1. تثبيت Flutter

#### Windows
```bash
# تحميل Flutter SDK
# استخراج الملفات إلى C:\flutter
# إضافة C:\flutter\bin إلى PATH

# التحقق من التثبيت
flutter doctor
```

#### macOS
```bash
# تحميل Flutter SDK
# استخراج الملفات إلى ~/flutter
# إضافة ~/flutter/bin إلى PATH

# التحقق من التثبيت
flutter doctor
```

#### Linux
```bash
# تحميل Flutter SDK
# استخراج الملفات إلى ~/flutter
# إضافة ~/flutter/bin إلى PATH

# التحقق من التثبيت
flutter doctor
```

### 2. إعداد المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd pos_app

# تثبيت المكتبات
flutter pub get

# تشغيل flutter doctor للتأكد من الإعداد
flutter doctor
```

### 3. إعداد قواعد البيانات

#### SQLite (محلي)
- يتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- مسار قاعدة البيانات: `<app_documents>/pos_app.db`

#### ERPNext (اختياري)
```bash
# متطلبات ERPNext
- ERPNext Server v13 أو أحدث
- API Key و API Secret
- اتصال إنترنت مستقر
```

### 4. إعداد البيئات المختلفة

#### Development
```bash
# تشغيل في وضع التطوير
flutter run --debug
```

#### Production
```bash
# بناء للإنتاج
flutter build apk --release  # Android
flutter build web --release  # Web
flutter build windows --release  # Windows
```

## إعداد ERPNext

### 1. إنشاء API User في ERPNext

```python
# في ERPNext Console
from frappe.auth import generate_keys
generate_keys("<EMAIL>")
```

### 2. إعداد الصلاحيات

```json
{
  "doctype": "User",
  "roles": [
    "Sales User",
    "Item Manager",
    "Accounts User"
  ],
  "api_key": "your_api_key",
  "api_secret": "your_api_secret"
}
```

### 3. اختبار الاتصال

```bash
# اختبار API
curl -X GET "https://your-erpnext-site.com/api/method/frappe.auth.get_logged_user" \
  -H "Authorization: token your_api_key:your_api_secret"
```

## إعداد قاعدة البيانات

### الجداول الأساسية

```sql
-- جدول المستخدمين
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  phone TEXT,
  role TEXT NOT NULL,
  permissions TEXT NOT NULL,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  synced INTEGER NOT NULL DEFAULT 0
);

-- جدول المنتجات
CREATE TABLE products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  barcode TEXT UNIQUE,
  category TEXT NOT NULL,
  price REAL NOT NULL,
  cost REAL NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 0,
  min_quantity INTEGER NOT NULL DEFAULT 0,
  unit TEXT NOT NULL,
  image_url TEXT,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  erp_data TEXT,
  synced INTEGER NOT NULL DEFAULT 0
);

-- جدول الفواتير
CREATE TABLE invoices (
  id TEXT PRIMARY KEY,
  invoice_number TEXT UNIQUE NOT NULL,
  customer_id TEXT,
  customer_name TEXT,
  customer_phone TEXT,
  cashier_id TEXT NOT NULL,
  cashier_name TEXT NOT NULL,
  subtotal REAL NOT NULL,
  tax_amount REAL NOT NULL DEFAULT 0,
  discount_amount REAL NOT NULL DEFAULT 0,
  total_amount REAL NOT NULL,
  paid_amount REAL NOT NULL,
  change_amount REAL NOT NULL DEFAULT 0,
  payment_method TEXT NOT NULL,
  status TEXT NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  erp_data TEXT,
  synced INTEGER NOT NULL DEFAULT 0
);

-- جدول عناصر الفواتير
CREATE TABLE invoice_items (
  id TEXT PRIMARY KEY,
  invoice_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  product_name TEXT NOT NULL,
  product_barcode TEXT,
  unit_price REAL NOT NULL,
  unit_cost REAL NOT NULL,
  quantity INTEGER NOT NULL,
  discount REAL NOT NULL DEFAULT 0,
  total_price REAL NOT NULL,
  FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
);

-- جدول الإعدادات
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL
);
```

### البيانات الأولية

```sql
-- المستخدمين الافتراضيين
INSERT INTO users VALUES 
('admin_001', 'admin', '<EMAIL>', 'مدير النظام', '1234567890', 'admin', '["create_user","edit_user","delete_user","view_users","create_product","edit_product","delete_product","view_products","create_invoice","edit_invoice","delete_invoice","view_invoices","view_reports","manage_settings","sync_data"]', 1, datetime('now'), NULL, 0),
('manager_001', 'manager', '<EMAIL>', 'مدير المبيعات', '1234567891', 'manager', '["view_users","create_product","edit_product","delete_product","view_products","create_invoice","edit_invoice","delete_invoice","view_invoices","view_reports","manage_settings","sync_data"]', 1, datetime('now'), NULL, 0),
('cashier_001', 'cashier', '<EMAIL>', 'أمين الصندوق', '1234567892', 'cashier', '["view_products","create_invoice","view_invoices"]', 1, datetime('now'), NULL, 0);
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في تثبيت المكتبات
```bash
# حذف pubspec.lock وإعادة التثبيت
rm pubspec.lock
flutter clean
flutter pub get
```

#### 2. مشاكل في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
# احذف ملف pos_app.db من مجلد التطبيق
# أعد تشغيل التطبيق
```

#### 3. مشاكل في الاتصال مع ERPNext
```bash
# تحقق من:
- صحة رابط ERPNext
- صحة API Key و API Secret
- اتصال الإنترنت
- إعدادات CORS في ERPNext
```

#### 4. مشاكل في تشغيل Windows
```bash
# تفعيل Developer Mode
# اذهب إلى Settings > Update & Security > For developers
# فعل Developer Mode
```

### سجلات الأخطاء

```bash
# عرض سجلات التطبيق
flutter logs

# تشغيل مع تفاصيل أكثر
flutter run --verbose
```

## الأمان

### تشفير البيانات
- كلمات المرور مشفرة باستخدام SHA-256
- بيانات API محفوظة في SharedPreferences مشفرة
- اتصال HTTPS مع ERPNext

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
cp <app_documents>/pos_app.db backup_$(date +%Y%m%d).db

# استعادة النسخة الاحتياطية
cp backup_20231201.db <app_documents>/pos_app.db
```

## الصيانة

### تحديث المكتبات
```bash
# تحديث المكتبات
flutter pub upgrade

# تحقق من التحديثات المتاحة
flutter pub outdated
```

### تنظيف المشروع
```bash
# تنظيف ملفات البناء
flutter clean

# إعادة بناء المشروع
flutter pub get
```

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. ابحث في Issues على GitHub
3. أنشئ Issue جديد مع تفاصيل المشكلة
4. تواصل مع فريق التطوير
