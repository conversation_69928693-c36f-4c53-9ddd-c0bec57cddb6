import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../data/services/database_service.dart';
import '../data/services/api_service.dart';

class LoginBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure services are initialized first
    Get.lazyPut<DatabaseService>(() => DatabaseService());
    Get.lazyPut<ApiService>(() => ApiService());
    
    // Initialize AuthController
    Get.lazyPut<AuthController>(() => AuthController());
  }
}
