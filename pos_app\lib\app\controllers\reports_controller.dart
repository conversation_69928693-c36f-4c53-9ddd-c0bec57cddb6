import 'package:get/get.dart';
import '../data/services/database_service.dart';
import '../data/models/invoice_model.dart';

class ReportsController extends GetxController {
  final DatabaseService _databaseService = Get.find<DatabaseService>();

  // Reactive variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<InvoiceModel> invoices = <InvoiceModel>[].obs;
  
  // Date range
  final Rx<DateTime> startDate = DateTime.now().subtract(const Duration(days: 30)).obs;
  final Rx<DateTime> endDate = DateTime.now().obs;
  
  // Report data
  final RxDouble totalSales = 0.0.obs;
  final RxDouble totalProfit = 0.0.obs;
  final RxInt totalInvoices = 0.obs;
  final RxMap<String, double> salesByCategory = <String, double>{}.obs;
  final RxMap<String, int> salesByPaymentMethod = <String, int>{}.obs;

  @override
  void onInit() {
    super.onInit();
    loadReports();
  }

  /// تحميل التقارير
  Future<void> loadReports() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // تحميل الفواتير في النطاق الزمني المحدد
      await _loadInvoicesInDateRange();
      
      // حساب الإحصائيات
      _calculateStatistics();
      
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل التقارير: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل الفواتير في النطاق الزمني
  Future<void> _loadInvoicesInDateRange() async {
    final allInvoices = await _databaseService.getAllInvoices();
    
    invoices.value = allInvoices.where((invoice) {
      final invoiceDate = invoice.createdAt;
      return invoiceDate.isAfter(startDate.value) && 
             invoiceDate.isBefore(endDate.value.add(const Duration(days: 1)));
    }).toList();
  }

  /// حساب الإحصائيات
  void _calculateStatistics() {
    // إجمالي المبيعات
    totalSales.value = invoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
    
    // إجمالي الربح (تقدير بسيط)
    totalProfit.value = invoices.fold(0.0, (sum, invoice) => sum + (invoice.totalAmount * 0.3));
    
    // عدد الفواتير
    totalInvoices.value = invoices.length;
    
    // المبيعات حسب طريقة الدفع
    salesByPaymentMethod.clear();
    for (final invoice in invoices) {
      salesByPaymentMethod[invoice.paymentMethod] = 
          (salesByPaymentMethod[invoice.paymentMethod] ?? 0) + 1;
    }
    
    // المبيعات حسب الفئة (تحتاج تحسين)
    salesByCategory.clear();
    salesByCategory['طعام'] = totalSales.value * 0.4;
    salesByCategory['مشروبات'] = totalSales.value * 0.3;
    salesByCategory['أخرى'] = totalSales.value * 0.3;
  }

  /// تحديد نطاق التاريخ
  void setDateRange(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;
    loadReports();
  }

  /// تصدير التقرير
  Future<void> exportReport() async {
    try {
      isLoading.value = true;
      
      // محاكاة تصدير التقرير
      await Future.delayed(const Duration(seconds: 2));
      
      Get.snackbar(
        'تم التصدير',
        'تم تصدير التقرير بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
      
    } catch (e) {
      errorMessage.value = 'خطأ في تصدير التقرير: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على أفضل المنتجات مبيعاً
  List<Map<String, dynamic>> getTopSellingProducts() {
    // محاكاة بيانات أفضل المنتجات
    return [
      {'name': 'كوكا كولا', 'quantity': 150, 'revenue': 375.0},
      {'name': 'خبز أبيض', 'quantity': 120, 'revenue': 120.0},
      {'name': 'حليب', 'quantity': 80, 'revenue': 360.0},
      {'name': 'أرز بسمتي', 'quantity': 45, 'revenue': 360.0},
    ];
  }

  /// الحصول على المبيعات اليومية
  List<Map<String, dynamic>> getDailySales() {
    final List<Map<String, dynamic>> dailySales = [];
    
    for (int i = 6; i >= 0; i--) {
      final date = DateTime.now().subtract(Duration(days: i));
      final dayInvoices = invoices.where((invoice) {
        final invoiceDate = invoice.createdAt;
        return invoiceDate.day == date.day && 
               invoiceDate.month == date.month && 
               invoiceDate.year == date.year;
      }).toList();
      
      final dayTotal = dayInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
      
      dailySales.add({
        'date': date,
        'total': dayTotal,
        'invoices': dayInvoices.length,
      });
    }
    
    return dailySales;
  }

  /// تحديث التقارير
  Future<void> refreshReports() async {
    await loadReports();
  }

  /// تنظيف الموارد
  @override
  void onClose() {
    // تنظيف إضافي إذا لزم الأمر
    super.onClose();
  }
}
