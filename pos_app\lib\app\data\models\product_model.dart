class ProductModel {
  final String id;
  final String name;
  final String description;
  final String barcode;
  final String category;
  final double price;
  final double cost;
  final int quantity;
  final int minQuantity;
  final String unit;
  final String? imageUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? erpData;

  ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.barcode,
    required this.category,
    required this.price,
    required this.cost,
    required this.quantity,
    required this.minQuantity,
    required this.unit,
    this.imageUrl,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.erpData,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      barcode: json['barcode'] ?? '',
      category: json['category'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      cost: (json['cost'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      minQuantity: json['min_quantity'] ?? 0,
      unit: json['unit'] ?? 'piece',
      imageUrl: json['image_url'],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      erpData: json['erp_data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'barcode': barcode,
      'category': category,
      'price': price,
      'cost': cost,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'unit': unit,
      'image_url': imageUrl,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'erp_data': erpData,
    };
  }

  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? barcode,
    String? category,
    double? price,
    double? cost,
    int? quantity,
    int? minQuantity,
    String? unit,
    String? imageUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? erpData,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      barcode: barcode ?? this.barcode,
      category: category ?? this.category,
      price: price ?? this.price,
      cost: cost ?? this.cost,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      unit: unit ?? this.unit,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      erpData: erpData ?? this.erpData,
    );
  }

  double get profit => price - cost;
  double get profitMargin => cost > 0 ? (profit / cost) * 100 : 0;
  bool get isLowStock => quantity <= minQuantity;
  double get totalValue => quantity * cost;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, barcode: $barcode, price: $price, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Product Categories
class ProductCategories {
  static const String food = 'food';
  static const String beverages = 'beverages';
  static const String electronics = 'electronics';
  static const String clothing = 'clothing';
  static const String health = 'health';
  static const String home = 'home';
  static const String books = 'books';
  static const String sports = 'sports';
  static const String toys = 'toys';
  static const String other = 'other';

  static List<String> get allCategories => [
    food, beverages, electronics, clothing, health,
    home, books, sports, toys, other,
  ];

  static Map<String, String> get categoryNames => {
    food: 'طعام',
    beverages: 'مشروبات',
    electronics: 'إلكترونيات',
    clothing: 'ملابس',
    health: 'صحة',
    home: 'منزل',
    books: 'كتب',
    sports: 'رياضة',
    toys: 'ألعاب',
    other: 'أخرى',
  };
}

// Product Units
class ProductUnits {
  static const String piece = 'piece';
  static const String kg = 'kg';
  static const String gram = 'gram';
  static const String liter = 'liter';
  static const String meter = 'meter';
  static const String box = 'box';
  static const String pack = 'pack';

  static List<String> get allUnits => [
    piece, kg, gram, liter, meter, box, pack,
  ];

  static Map<String, String> get unitNames => {
    piece: 'قطعة',
    kg: 'كيلوجرام',
    gram: 'جرام',
    liter: 'لتر',
    meter: 'متر',
    box: 'صندوق',
    pack: 'عبوة',
  };
}
