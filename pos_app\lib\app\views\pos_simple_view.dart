import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/pos_controller.dart';

class POSView extends GetView<POSController> {
  const POSView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نقاط البيع'),
        actions: [
          Obx(() => IconButton(
            icon: Badge(
              label: Text('${controller.invoiceController.currentInvoiceItems.length}'),
              child: const Icon(Icons.shopping_cart),
            ),
            onPressed: () => _showMessage(context, 'سلة التسوق'),
          )),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.point_of_sale,
              size: 100,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            const Text(
              'واجهة نقاط البيع',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Error/Success messages
            Obx(() {
              if (controller.errorMessage.value.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    border: Border.all(color: Colors.red),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    controller.errorMessage.value,
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              }
              
              if (controller.successMessage.value.isNotEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    border: Border.all(color: Colors.green),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    controller.successMessage.value,
                    style: const TextStyle(color: Colors.green),
                  ),
                );
              }
              
              return const SizedBox.shrink();
            }),
            
            // Barcode input
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'مسح الباركود',
                  prefixIcon: Icon(Icons.qr_code_scanner),
                  border: OutlineInputBorder(),
                ),
                onSubmitted: (value) => controller.onBarcodeSubmitted(value),
              ),
            ),
            
            // Current invoice info
            Obx(() => Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'عدد العناصر: ${controller.invoiceController.currentInvoiceItems.length}',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الإجمالي: ${controller.invoiceController.totalAmount.value.toStringAsFixed(2)} ر.س',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            )),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => controller.clearInvoice(),
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح'),
                ),
                ElevatedButton.icon(
                  onPressed: controller.canProcessSale
                      ? () => _processSale(context)
                      : null,
                  icon: const Icon(Icons.payment),
                  label: const Text('إتمام البيع'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _processSale(BuildContext context) async {
    // Simple sale processing
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إتمام البيع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الإجمالي: ${controller.invoiceController.totalAmount.value.toStringAsFixed(2)} ر.س'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'المبلغ المدفوع',
                suffixText: 'ر.س',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) => controller.paidAmount.value = value,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final result = await controller.processSale();
              if (context.mounted) {
                Navigator.of(context).pop();
                if (result != null) {
                  _showMessage(context, 'تم إتمام البيع بنجاح - رقم الفاتورة: $result');
                }
              }
            },
            child: const Text('إتمام'),
          ),
        ],
      ),
    );
  }
}
