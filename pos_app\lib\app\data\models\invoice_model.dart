import 'product_model.dart';

class InvoiceModel {
  final String id;
  final String invoiceNumber;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final String cashierId;
  final String cashierName;
  final List<InvoiceItemModel> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final double changeAmount;
  final String paymentMethod;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? erpData;

  InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.cashierId,
    required this.cashierName,
    required this.items,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.changeAmount,
    required this.paymentMethod,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.erpData,
  });

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      id: json['id'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      customerId: json['customer_id'] ?? '',
      customerName: json['customer_name'] ?? '',
      customerPhone: json['customer_phone'] ?? '',
      cashierId: json['cashier_id'] ?? '',
      cashierName: json['cashier_name'] ?? '',
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => InvoiceItemModel.fromJson(item))
          .toList() ?? [],
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      taxAmount: (json['tax_amount'] ?? 0).toDouble(),
      discountAmount: (json['discount_amount'] ?? 0).toDouble(),
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      paidAmount: (json['paid_amount'] ?? 0).toDouble(),
      changeAmount: (json['change_amount'] ?? 0).toDouble(),
      paymentMethod: json['payment_method'] ?? PaymentMethods.cash,
      status: json['status'] ?? InvoiceStatus.completed,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      erpData: json['erp_data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'cashier_id': cashierId,
      'cashier_name': cashierName,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'change_amount': changeAmount,
      'payment_method': paymentMethod,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'erp_data': erpData,
    };
  }

  InvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? cashierId,
    String? cashierName,
    List<InvoiceItemModel>? items,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    double? changeAmount,
    String? paymentMethod,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? erpData,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      cashierId: cashierId ?? this.cashierId,
      cashierName: cashierName ?? this.cashierName,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      changeAmount: changeAmount ?? this.changeAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      erpData: erpData ?? this.erpData,
    );
  }

  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);
  double get totalProfit => items.fold(0, (sum, item) => sum + item.profit);

  @override
  String toString() {
    return 'InvoiceModel(id: $id, invoiceNumber: $invoiceNumber, totalAmount: $totalAmount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class InvoiceItemModel {
  final String id;
  final String productId;
  final String productName;
  final String productBarcode;
  final double unitPrice;
  final double unitCost;
  final int quantity;
  final double discount;
  final double totalPrice;

  InvoiceItemModel({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productBarcode,
    required this.unitPrice,
    required this.unitCost,
    required this.quantity,
    required this.discount,
    required this.totalPrice,
  });

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    return InvoiceItemModel(
      id: json['id'] ?? '',
      productId: json['product_id'] ?? '',
      productName: json['product_name'] ?? '',
      productBarcode: json['product_barcode'] ?? '',
      unitPrice: (json['unit_price'] ?? 0).toDouble(),
      unitCost: (json['unit_cost'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      discount: (json['discount'] ?? 0).toDouble(),
      totalPrice: (json['total_price'] ?? 0).toDouble(),
    );
  }

  factory InvoiceItemModel.fromProduct(ProductModel product, int quantity) {
    final totalPrice = product.price * quantity;
    return InvoiceItemModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: product.id,
      productName: product.name,
      productBarcode: product.barcode,
      unitPrice: product.price,
      unitCost: product.cost,
      quantity: quantity,
      discount: 0,
      totalPrice: totalPrice,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_barcode': productBarcode,
      'unit_price': unitPrice,
      'unit_cost': unitCost,
      'quantity': quantity,
      'discount': discount,
      'total_price': totalPrice,
    };
  }

  InvoiceItemModel copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productBarcode,
    double? unitPrice,
    double? unitCost,
    int? quantity,
    double? discount,
    double? totalPrice,
  }) {
    return InvoiceItemModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productBarcode: productBarcode ?? this.productBarcode,
      unitPrice: unitPrice ?? this.unitPrice,
      unitCost: unitCost ?? this.unitCost,
      quantity: quantity ?? this.quantity,
      discount: discount ?? this.discount,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  double get profit => (unitPrice - unitCost) * quantity - discount;
  double get discountedPrice => totalPrice - discount;

  @override
  String toString() {
    return 'InvoiceItemModel(productName: $productName, quantity: $quantity, totalPrice: $totalPrice)';
  }
}

// Invoice Status
class InvoiceStatus {
  static const String draft = 'draft';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';
  static const String refunded = 'refunded';

  static List<String> get allStatuses => [draft, completed, cancelled, refunded];

  static Map<String, String> get statusNames => {
    draft: 'مسودة',
    completed: 'مكتملة',
    cancelled: 'ملغية',
    refunded: 'مسترجعة',
  };
}

// Payment Methods
class PaymentMethods {
  static const String cash = 'cash';
  static const String card = 'card';
  static const String mobile = 'mobile';
  static const String bank = 'bank';

  static List<String> get allMethods => [cash, card, mobile, bank];

  static Map<String, String> get methodNames => {
    cash: 'نقدي',
    card: 'بطاقة',
    mobile: 'محفظة إلكترونية',
    bank: 'تحويل بنكي',
  };
}
