class UserModel {
  final String id;
  final String username;
  final String email;
  final String fullName;
  final String phone;
  final String role;
  final List<String> permissions;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  UserModel({
    required this.id,
    required this.username,
    required this.email,
    required this.fullName,
    required this.phone,
    required this.role,
    required this.permissions,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? '',
      phone: json['phone'] ?? '',
      role: json['role'] ?? '',
      permissions: List<String>.from(json['permissions'] ?? []),
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'full_name': fullName,
      'phone': phone,
      'role': role,
      'permissions': permissions,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    String? fullName,
    String? phone,
    String? role,
    List<String>? permissions,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, email: $email, fullName: $fullName, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// User Roles
class UserRoles {
  static const String admin = 'admin';
  static const String manager = 'manager';
  static const String cashier = 'cashier';
  static const String viewer = 'viewer';

  static List<String> get allRoles => [admin, manager, cashier, viewer];
}

// User Permissions
class UserPermissions {
  static const String createUser = 'create_user';
  static const String editUser = 'edit_user';
  static const String deleteUser = 'delete_user';
  static const String viewUsers = 'view_users';
  
  static const String createProduct = 'create_product';
  static const String editProduct = 'edit_product';
  static const String deleteProduct = 'delete_product';
  static const String viewProducts = 'view_products';
  
  static const String createInvoice = 'create_invoice';
  static const String editInvoice = 'edit_invoice';
  static const String deleteInvoice = 'delete_invoice';
  static const String viewInvoices = 'view_invoices';
  
  static const String viewReports = 'view_reports';
  static const String manageSettings = 'manage_settings';
  static const String syncData = 'sync_data';

  static List<String> get allPermissions => [
    createUser, editUser, deleteUser, viewUsers,
    createProduct, editProduct, deleteProduct, viewProducts,
    createInvoice, editInvoice, deleteInvoice, viewInvoices,
    viewReports, manageSettings, syncData,
  ];

  static Map<String, List<String>> get rolePermissions => {
    UserRoles.admin: allPermissions,
    UserRoles.manager: [
      viewUsers, createProduct, editProduct, deleteProduct, viewProducts,
      createInvoice, editInvoice, deleteInvoice, viewInvoices,
      viewReports, manageSettings, syncData,
    ],
    UserRoles.cashier: [
      viewProducts, createInvoice, viewInvoices,
    ],
    UserRoles.viewer: [
      viewUsers, viewProducts, viewInvoices, viewReports,
    ],
  };
}
