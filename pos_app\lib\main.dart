import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app/data/services/database_service.dart';
import 'app/data/services/api_service.dart';
import 'app/controllers/auth_controller.dart';
import 'app/controllers/product_controller.dart';
import 'app/controllers/invoice_controller.dart';
import 'app/controllers/pos_controller.dart';
import 'app/routes/app_pages.dart';
import 'app/routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await initServices();

  runApp(const POSApp());
}

Future<void> initServices() async {
  // Initialize database service
  Get.put(DatabaseService());

  // Initialize API service
  Get.put(ApiService());

  // Initialize auth controller
  Get.put(AuthController());

  // Initialize product controller
  Get.put(ProductController());

  // Initialize invoice controller
  Get.put(InvoiceController());

  // Initialize POS controller
  Get.put(POSController());

  // Initialize default users
  final authController = Get.find<AuthController>();
  await authController.initializeDefaultUsers();
}

class POSApp extends StatelessWidget {
  const POSApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'نظام نقاط البيع',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        fontFamily: 'Arial',
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
      initialRoute: AppRoutes.splash,
      getPages: AppPages.routes,
      debugShowCheckedModeBanner: false,
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}


