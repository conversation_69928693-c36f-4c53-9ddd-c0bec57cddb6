import 'package:get/get.dart';
import '../views/splash_view.dart';
import '../views/login_view.dart';
import '../views/home_view.dart';
import '../views/pos_simple_view.dart';
import '../views/products_working_view.dart';
import '../views/users_view.dart';
import '../views/invoices_view.dart';
import '../views/settings_view.dart';
import '../views/debug_view.dart';
import '../views/reports_with_binding_view.dart' as reports_binding;
import '../bindings/reports_binding.dart';
import 'app_routes.dart';

class AppPages {
  static final routes = [
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashView(),
    ),
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginView(),
    ),
    GetPage(
      name: AppRoutes.home,
      page: () => const HomeView(),
    ),
    GetPage(
      name: AppRoutes.pos,
      page: () => const POSView(),
    ),
    GetPage(
      name: AppRoutes.products,
      page: () => const ProductsView(),
    ),
    GetPage(
      name: AppRoutes.users,
      page: () => const UsersView(),
    ),
    GetPage(
      name: AppRoutes.invoices,
      page: () => const InvoicesView(),
    ),
    GetPage(
      name: AppRoutes.reports,
      page: () => const reports_binding.ReportsView(),
      binding: ReportsBinding(),
    ),
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsView(),
    ),
    GetPage(
      name: AppRoutes.debug,
      page: () => const DebugView(),
    ),
  ];
}
